                  variant="outline" 
                  size="sm" 
                  onClick={handleDismiss}
                  className="border-blue-300 text-blue-700 hover:bg-blue-100"
                >
                  Dismiss
                </Button>
              </div>
            </div>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="ml-2 h-6 w-6 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>
        
        <JoinOrganizationModal
          isOpen={showJoinModal}
          onClose={handleJoinModalClose}
          detectionResult={detectionResult}
          userEmail={userEmail}
        />
      </>
    )
  }
  
  // Toast variant (compact notification)
  if (variant === 'toast') {
    return (
      <>
        <div className={`flex items-center gap-3 p-3 bg-blue-50 border border-blue-200 rounded-lg ${className}`}>
          <Building2 className="h-4 w-4 text-blue-600 flex-shrink-0" />
          
          <div className="flex-1 text-sm">
            <span className="font-medium text-blue-900">
              {organization.name}
            </span>
            <span className="text-blue-700 ml-1">
              organization detected
            </span>
          </div>
          
          {getStatusBadge()}
          
          <div className="flex gap-1">
            {!detectionResult.hasPendingRequest && (
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => setShowJoinModal(true)}
                className="h-7 px-2 text-xs border-blue-300 text-blue-700 hover:bg-blue-100"
              >
                {organization.autoJoinEnabled ? 'Join' : 'Request'}
              </Button>
            )}
            
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-7 w-7 p-0 text-blue-600 hover:bg-blue-100"
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <JoinOrganizationModal
          isOpen={showJoinModal}
          onClose={handleJoinModalClose}
          detectionResult={detectionResult}
          userEmail={userEmail}
        />
      </>
    )
  }
  
  return null
}

/**
 * Multiple Organizations Notification
 * 
 * Displays notification when multiple organizations are detected
 */
interface MultipleOrgsNotificationProps {
  organizations: Array<{
    id: string
    name: string
    domain: string
    autoJoinEnabled: boolean
  }>
  userEmail: string
  onSelectOrganization?: (orgId: string) => void
  onDismiss?: () => void
  className?: string
}

export function MultipleOrgsNotification({
  organizations,
  userEmail,
  onSelectOrganization,
  onDismiss,
  className
}: MultipleOrgsNotificationProps) {
  const [isDismissed, setIsDismissed] = useState(false)
  
  if (organizations.length <= 1 || isDismissed) {
    return null
  }
  
  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }
  
  return (
    <Alert className={`border-orange-200 bg-orange-50 ${className}`}>
      <Building2 className="h-4 w-4" />
      <div className="flex items-start justify-between w-full">
        <div className="space-y-3 flex-1">
          <AlertDescription className="text-orange-800">
            <strong>Multiple organizations detected!</strong>
            <span className="block mt-1">
              Your email domain is associated with {organizations.length} organizations. 
              Choose which one you'd like to join:
            </span>
          </AlertDescription>
          
          <div className="space-y-2">
            {organizations.map((org) => (
              <div 
                key={org.id} 
                className="flex items-center justify-between p-2 bg-white rounded border"
              >
                <div>
                  <span className="font-medium">{org.name}</span>
                  <span className="text-sm text-gray-600 ml-2">({org.domain})</span>
                </div>
                <div className="flex items-center gap-2">
                  {org.autoJoinEnabled ? (
                    <Badge variant="outline" className="bg-green-100 text-green-800">
                      Auto-join
                    </Badge>
                  ) : (
                    <Badge variant="outline" className="bg-yellow-100 text-yellow-800">
                      Approval required
                    </Badge>
                  )}
                  <Button 
                    size="sm" 
                    onClick={() => onSelectOrganization?.(org.id)}
                  >
                    Select
                  </Button>
                </div>
              </div>
            ))}
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleDismiss}
          >
            Dismiss
          </Button>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="ml-2 h-6 w-6 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  )
}

/**
 * Organization Request Status Notification
 * 
 * Shows updates on join request status changes
 */
interface OrgRequestStatusNotificationProps {
  status: 'approved' | 'rejected' | 'expired'
  organizationName: string
  adminNotes?: string
  onDismiss?: () => void
  className?: string
}

export function OrgRequestStatusNotification({
  status,
  organizationName,
  adminNotes,
  onDismiss,
  className
}: OrgRequestStatusNotificationProps) {
  const [isDismissed, setIsDismissed] = useState(false)
  
  if (isDismissed) return null
  
  const statusConfig = {
    approved: {
      color: 'border-green-200 bg-green-50',
      textColor: 'text-green-800',
      icon: CheckCircle,
      title: 'Join Request Approved!',
      message: `Your request to join ${organizationName} has been approved.`
    },
    rejected: {
      color: 'border-red-200 bg-red-50',
      textColor: 'text-red-800',
      icon: X,
      title: 'Join Request Declined',
      message: `Your request to join ${organizationName} was not approved.`
    },
    expired: {
      color: 'border-gray-200 bg-gray-50',
      textColor: 'text-gray-800',
      icon: Clock,
      title: 'Join Request Expired',
      message: `Your request to join ${organizationName} has expired.`
    }
  }
  
  const config = statusConfig[status]
  const Icon = config.icon
  
  const handleDismiss = () => {
    setIsDismissed(true)
    onDismiss?.()
  }
  
  return (
    <Alert className={`${config.color} ${className}`}>
      <Icon className="h-4 w-4" />
      <div className="flex items-start justify-between w-full">
        <div className="space-y-2 flex-1">
          <AlertDescription className={config.textColor}>
            <strong>{config.title}</strong>
            <span className="block mt-1">{config.message}</span>
            {adminNotes && (
              <div className="mt-2 p-2 bg-white rounded border">
                <span className="text-sm font-medium">Admin response:</span>
                <p className="text-sm mt-1">"{adminNotes}"</p>
              </div>
            )}
          </AlertDescription>
          
          <Button 
            variant="outline" 
            size="sm" 
            onClick={handleDismiss}
          >
            Dismiss
          </Button>
        </div>
        
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="ml-2 h-6 w-6 p-0"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    </Alert>
  )
}
