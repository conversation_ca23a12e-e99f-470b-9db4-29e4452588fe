'use client'

import { useState, useEffect, useCallback } from 'react'
import { useUser } from '@clerk/nextjs'
import { 
  getUserPreferences, 
  getUserPreferencesWithOrganization,
  updateUserPreferences,
  createUserPreferences,
  type UserPreferences,
  type UserPreferencesWithOrg,
  type UpdateUserPreferencesInput,
  type CreateUserPreferencesInput
} from '@/lib/services/user-preferences'
import { 
  validateAndSuggestUsername,
  type UsernameValidationResult
} from '@/lib/services/username-validation'

/**
 * Form state interface for the preferences modal
 */
export interface PreferencesFormState {
  // Profile
  firstName: string
  lastName: string
  username: string
  displayName: string
  
  // Display
  theme: 'light' | 'dark' | 'system'
  timezone: string
  processContext: 'recruitment' | 'bench_sales' | 'both'
  
  // Validation
  errors: Record<string, string[]>
  isSubmitting: boolean
  isDirty: boolean
}

/**
 * Hook for managing user preferences state and operations
 */
export function usePreferences() {
  const { user, isLoaded } = useUser()
  
  // State
  const [preferences, setPreferences] = useState<UserPreferencesWithOrg | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  
  // Form state
  const [formState, setFormState] = useState<PreferencesFormState>({
    firstName: '',
    lastName: '',
    username: '',
    displayName: '',
    theme: 'system',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'America/Chicago',
    processContext: 'both',
    errors: {},
    isSubmitting: false,
    isDirty: false
  })
  
  // Username validation state
  const [usernameValidation, setUsernameValidation] = useState<UsernameValidationResult | null>(null)
  const [isValidatingUsername, setIsValidatingUsername] = useState(false)

  /**
   * Load user preferences from the database
   */
  const loadPreferences = useCallback(async () => {
    if (!user?.id) return
    
    try {
      setIsLoading(true)
      setError(null)
      
      const userPrefs = await getUserPreferencesWithOrganization(user.id)
      setPreferences(userPrefs)
      
      // Initialize form state with loaded preferences or defaults
      if (userPrefs) {
        setFormState(prev => ({
          ...prev,
          firstName: userPrefs.firstName,
          lastName: userPrefs.lastName,
          username: userPrefs.username,
          displayName: userPrefs.displayName || '',
          theme: userPrefs.theme,
          timezone: userPrefs.timezone,
          processContext: userPrefs.processContext,
          isDirty: false
        }))
      } else {
        // Set defaults from Clerk user data if available
        setFormState(prev => ({
          ...prev,
          firstName: user.firstName || '',
          lastName: user.lastName || '',
          username: generateDefaultUsername(user.firstName || '', user.lastName || ''),
          isDirty: false
        }))
      }
    } catch (err) {
      console.error('Failed to load preferences:', err)
      setError('Failed to load preferences')
    } finally {
      setIsLoading(false)
    }
  }, [user?.id, user?.firstName, user?.lastName])

  /**
   * Update form field value
   */
  const updateField = useCallback((field: keyof PreferencesFormState, value: any) => {
    setFormState(prev => ({
      ...prev,
      [field]: value,
      isDirty: true,
      errors: {
        ...prev.errors,
        [field]: [] // Clear errors for this field
      }
    }))
  }, [])

  /**
   * Validate username with debouncing
   */
  const validateUsername = useCallback(async (username: string) => {
    if (!username.trim()) {
      setUsernameValidation(null)
      return
    }
    
    try {
      setIsValidatingUsername(true)
      const result = await validateAndSuggestUsername(
        username,
        formState.firstName,
        formState.lastName
      )
      setUsernameValidation(result)
      
      // Update form errors
      if (!result.isValid) {
        setFormState(prev => ({
          ...prev,
          errors: {
            ...prev.errors,
            username: result.errors
          }
        }))
      }
    } catch (err) {
      console.error('Username validation failed:', err)
    } finally {
      setIsValidatingUsername(false)
    }
  }, [formState.firstName, formState.lastName])

  /**
   * Save preferences to database
   */
  const savePreferences = useCallback(async (): Promise<{ success: boolean; error?: string }> => {
    if (!user?.id) {
      return { success: false, error: 'User not authenticated' }
    }

    try {
      setFormState(prev => ({ ...prev, isSubmitting: true }))
      setError(null)

      // Prepare update data
      const updateData: UpdateUserPreferencesInput = {
        firstName: formState.firstName.trim(),
        lastName: formState.lastName.trim(),
        username: formState.username.trim(),
        displayName: formState.displayName.trim() || undefined,
        theme: formState.theme,
        timezone: formState.timezone,
        processContext: formState.processContext
      }

      let result
      if (preferences) {
        // Update existing preferences
        result = await updateUserPreferences(user.id, updateData)
      } else {
        // Create new preferences
        const createData: CreateUserPreferencesInput = {
          userId: user.id,
          firstName: updateData.firstName!,
          lastName: updateData.lastName!,
          username: updateData.username!,
          displayName: updateData.displayName,
          theme: updateData.theme,
          timezone: updateData.timezone,
          processContext: updateData.processContext,
          email: user.emailAddresses[0]?.emailAddress
        }
        result = await createUserPreferences(createData)
      }

      if (result.success && result.preferences) {
        // Reload preferences to get the latest data
        await loadPreferences()
        setFormState(prev => ({ ...prev, isDirty: false }))
        return { success: true }
      } else {
        setError(result.error || 'Failed to save preferences')
        return { success: false, error: result.error }
      }
    } catch (err) {
      console.error('Failed to save preferences:', err)
      const errorMessage = 'An unexpected error occurred'
      setError(errorMessage)
      return { success: false, error: errorMessage }
    } finally {
      setFormState(prev => ({ ...prev, isSubmitting: false }))
    }
  }, [user?.id, user?.emailAddresses, formState, preferences, loadPreferences])

  /**
   * Reset form to original state
   */
  const resetForm = useCallback(() => {
    if (preferences) {
      setFormState(prev => ({
        ...prev,
        firstName: preferences.firstName,
        lastName: preferences.lastName,
        username: preferences.username,
        displayName: preferences.displayName || '',
        theme: preferences.theme,
        timezone: preferences.timezone,
        processContext: preferences.processContext,
        errors: {},
        isDirty: false
      }))
    }
    setUsernameValidation(null)
    setError(null)
  }, [preferences])

  // Load preferences when user is available
  useEffect(() => {
    if (isLoaded && user?.id) {
      loadPreferences()
    }
  }, [isLoaded, user?.id, loadPreferences])

  return {
    // Data
    preferences,
    formState,
    usernameValidation,
    isValidatingUsername,
    
    // Loading states
    isLoading,
    error,
    
    // Actions
    updateField,
    validateUsername,
    savePreferences,
    resetForm,
    loadPreferences
  }
}

/**
 * Generate a default username from first and last name
 */
function generateDefaultUsername(firstName: string, lastName: string): string {
  const cleanFirst = firstName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  const cleanLast = lastName.toLowerCase().replace(/[^a-zA-Z0-9]/g, '')
  
  if (cleanFirst && cleanLast) {
    return `${cleanFirst}.${cleanLast}`
  }
  
  return cleanFirst || cleanLast || 'user'
}