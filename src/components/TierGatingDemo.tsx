'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { 
  UpgradeToTeamModal, 
  OrganizationUpgradePrompt, 
  TierGatedFeature, 
  OrgGatedFeature, 
  AdminGatedFeature 
} from '@/components/billing'
import { AdminAccessGate, FirstAdminCreation } from '@/components/admin'
import { Crown, Users, Building, Settings, Zap } from 'lucide-react'
import type { AccountTier } from '@/lib/services/tier-gating'

/**
 * Phase 3.5 Demo Component
 * 
 * Comprehensive demonstration of the tier-gated organization access system.
 * Shows all components and workflows for different account tiers and scenarios.
 */

export function TierGatingDemo() {
  const [currentTier, setCurrentTier] = useState<AccountTier>('free')
  const [isOrgAdmin, setIsOrgAdmin] = useState(false)
  const [showUpgradeModal, setShowUpgradeModal] = useState(false)
  const [showFirstAdminModal, setShowFirstAdminModal] = useState(false)

  const tierColors = {
    free: 'bg-gray-100 text-gray-800',
    pro: 'bg-blue-100 text-blue-800',
    team: 'bg-amber-100 text-amber-800',
    enterprise: 'bg-purple-100 text-purple-800'
  }

  const handleUpgrade = async (tier: 'team', billing: 'monthly' | 'annually') => {
    console.log(`Upgrading to ${tier} with ${billing} billing`)
    // Simulate upgrade
    setCurrentTier('team')
    setShowUpgradeModal(false)
  }

  const handleBecomeAdmin = async () => {
    console.log('Becoming organization admin')
    setIsOrgAdmin(true)
    setShowFirstAdminModal(false)
  }

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header and Controls */}
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">Phase 3.5: Tier-Gated Organization Access</h1>
            <p className="text-muted-foreground">
              Enterprise-grade tier-based access control demonstration
            </p>
          </div>
          <Badge className={tierColors[currentTier]} variant="secondary">
            <Crown className="h-3 w-3 mr-1" />
            {currentTier} Plan
          </Badge>
        </div>

        {/* Tier Controls */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Demo Controls</CardTitle>
            <CardDescription>
              Change account tier and admin status to test different scenarios
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <span className="text-sm font-medium">Account Tier:</span>
              {(['free', 'pro', 'team', 'enterprise'] as AccountTier[]).map((tier) => (
                <Button
                  key={tier}
                  size="sm"
                  variant={currentTier === tier ? 'default' : 'outline'}
                  onClick={() => setCurrentTier(tier)}
                >
                  {tier}
                </Button>
              ))}
            </div>
            <div className="flex gap-2 items-center">
              <span className="text-sm font-medium">Organization Admin:</span>
              <Button
                size="sm"
                variant={isOrgAdmin ? 'default' : 'outline'}
                onClick={() => setIsOrgAdmin(!isOrgAdmin)}
              >
                {isOrgAdmin ? 'Yes' : 'No'}
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tier-Gated Features Demo */}
      <Tabs defaultValue="features" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="features">Gated Features</TabsTrigger>
          <TabsTrigger value="prompts">Upgrade Prompts</TabsTrigger>
          <TabsTrigger value="admin">Admin Controls</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
        </TabsList>

        {/* Tier-Gated Features */}
        <TabsContent value="features" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            {/* Organization Access */}
            <OrgGatedFeature 
              currentTier={currentTier}
              onUpgradeClick={() => setShowUpgradeModal(true)}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-5 w-5" />
                    Team Collaboration
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Collaborate with your team members, share workflows, and manage projects together.
                  </p>
                </CardContent>
              </Card>
            </OrgGatedFeature>

            {/* Create Organization */}
            <TierGatedFeature
              requiredTier="enterprise"
              currentTier={currentTier}
              feature="createOrganization"
              onUpgradeClick={() => setShowUpgradeModal(true)}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Building className="h-5 w-5" />
                    Create Organization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Create and manage multiple organizations with advanced controls.
                  </p>
                </CardContent>
              </Card>
            </TierGatedFeature>

            {/* Admin Features */}
            <AdminGatedFeature
              currentTier={currentTier}
              onUpgradeClick={() => setShowUpgradeModal(true)}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Settings className="h-5 w-5" />
                    Admin Dashboard
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Access administrative features and manage team settings.
                  </p>
                </CardContent>
              </Card>
            </AdminGatedFeature>

            {/* Invite Members */}
            <TierGatedFeature
              requiredTier="team"
              currentTier={currentTier}
              feature="inviteMembers"
              onUpgradeClick={() => setShowUpgradeModal(true)}
            >
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Zap className="h-5 w-5" />
                    Invite Members
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground">
                    Invite team members and manage their access permissions.
                  </p>
                </CardContent>
              </Card>
            </TierGatedFeature>
          </div>
        </TabsContent>

        {/* Upgrade Prompts */}
        <TabsContent value="prompts" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Organization Upgrade Prompt - Card Variant</CardTitle>
              </CardHeader>
              <CardContent>
                <OrganizationUpgradePrompt
                  currentTier={currentTier}
                  organizationName="Acme Corporation"
                  context="domain_detection"
                  variant="card"
                  memberCount={25}
                  onUpgrade={() => setShowUpgradeModal(true)}
                  onDismiss={() => console.log('Dismissed')}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Organization Upgrade Prompt - Banner Variant</CardTitle>
              </CardHeader>
              <CardContent>
                <OrganizationUpgradePrompt
                  currentTier={currentTier}
                  organizationName="Acme Corporation"
                  context="join_attempt"
                  variant="banner"
                  onUpgrade={() => setShowUpgradeModal(true)}
                  onDismiss={() => console.log('Dismissed')}
                />
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Organization Upgrade Prompt - Inline Variant</CardTitle>
              </CardHeader>
              <CardContent>
                <OrganizationUpgradePrompt
                  currentTier={currentTier}
                  context="admin_attempt"
                  variant="inline"
                  onUpgrade={() => setShowUpgradeModal(true)}
                />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Admin Controls */}
        <TabsContent value="admin" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Admin Access Gate - Card Variant</CardTitle>
                <CardDescription>
                  Current tier: {currentTier} | Admin status: {isOrgAdmin ? 'Yes' : 'No'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <AdminAccessGate
                  accountTier={currentTier}
                  isOrgAdmin={isOrgAdmin}
                  capability="invite_members"
                  fallbackVariant="card"
                  onUpgradeClick={() => setShowUpgradeModal(true)}
                >
                  <Card className="border-green-200 bg-green-50">
                    <CardContent className="p-4">
                      <div className="flex items-center gap-2">
                        <Settings className="h-5 w-5 text-green-600" />
                        <span className="font-medium text-green-900">
                          Admin Feature Unlocked!
                        </span>
                      </div>
                    </CardContent>
                  </Card>
                </AdminAccessGate>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Admin Access Gate - Alert Variant</CardTitle>
              </CardHeader>
              <CardContent>
                <AdminAccessGate
                  accountTier={currentTier}
                  isOrgAdmin={isOrgAdmin}
                  capability="manage_domains"
                  fallbackVariant="alert"
                  onUpgradeClick={() => setShowUpgradeModal(true)}
                >
                  <div className="p-4 bg-green-50 border border-green-200 rounded">
                    <p className="text-green-800">Advanced admin feature accessible!</p>
                  </div>
                </AdminAccessGate>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Admin Access Gate - Minimal Variant</CardTitle>
              </CardHeader>
              <CardContent>
                <AdminAccessGate
                  accountTier={currentTier}
                  isOrgAdmin={isOrgAdmin}
                  capability="all"
                  fallbackVariant="minimal"
                  onUpgradeClick={() => setShowUpgradeModal(true)}
                >
                  <div className="p-4 bg-purple-50 border border-purple-200 rounded">
                    <p className="text-purple-800">Enterprise admin feature accessible!</p>
                  </div>
                </AdminAccessGate>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Workflows */}
        <TabsContent value="workflows" className="space-y-6">
          <div className="grid gap-6">
            <Card>
              <CardHeader>
                <CardTitle>First Admin Creation Workflow</CardTitle>
                <CardDescription>
                  Simulate the first Team+ user becoming an organization admin
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowFirstAdminModal(true)}>
                  Open First Admin Creation
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Upgrade to Team Modal</CardTitle>
                <CardDescription>
                  Professional upgrade flow for team collaboration
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button onClick={() => setShowUpgradeModal(true)}>
                  <Crown className="h-4 w-4 mr-2" />
                  Open Upgrade Modal
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Modals */}
      <UpgradeToTeamModal
        isOpen={showUpgradeModal}
        onClose={() => setShowUpgradeModal(false)}
        currentTier={currentTier}
        organizationName="Acme Corporation"
        context="domain_detection"
        onUpgrade={handleUpgrade}
      />

      <FirstAdminCreation
        isOpen={showFirstAdminModal}
        onClose={() => setShowFirstAdminModal(false)}
        organizationName="Acme Corporation"
        userInfo={{
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>"
        }}
        onBecomeAdmin={handleBecomeAdmin}
        onDecline={() => setShowFirstAdminModal(false)}
      />
    </div>
  )
}
