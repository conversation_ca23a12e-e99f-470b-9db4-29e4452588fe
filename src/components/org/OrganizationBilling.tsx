"use client"

import * as React from "react"
import { Organization } from "@clerk/nextjs/server"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  CreditCard, 
  Crown, 
  Users, 
  Database,
  Zap,
  BarChart3,
  ExternalLink,
  Check,
  X,
  AlertTriangle,
  TrendingUp,
  Calendar,
  DollarSign
} from "lucide-react"

interface OrganizationBillingProps {
  organization: Organization
}

interface BillingData {
  currentPlan: {
    name: string
    tier: 'free' | 'pro' | 'team' | 'enterprise'
    price: number
    billingPeriod: 'monthly' | 'yearly'
    features: string[]
  }
  usage: {
    members: { current: number; limit: number }
    storage: { current: number; limit: number }
    apiCalls: { current: number; limit: number }
  }
  nextBilling: {
    date: string
    amount: number
  }
  paymentMethod: {
    type: string
    last4: string
    expiry: string
  } | null
}

export function OrganizationBilling({ organization }: OrganizationBillingProps) {
  const [billingData, setBillingData] = React.useState<BillingData | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchBillingData = async () => {
      try {
        // TODO: Implement billing API integration
        const mockData: BillingData = {
          currentPlan: {
            name: 'Pro Plan',
            tier: 'pro',
            price: 29,
            billingPeriod: 'monthly',
            features: [
              'Up to 50 team members',
              '100GB storage',
              '10,000 API calls/month',
              'Advanced analytics',
              'Priority support'
            ]
          },
          usage: {
            members: { current: 8, limit: 50 },
            storage: { current: 23, limit: 100 },
            apiCalls: { current: 2430, limit: 10000 }
          },
          nextBilling: {
            date: '2024-02-15',
            amount: 29
          },
          paymentMethod: {
            type: 'Visa',
            last4: '4242',
            expiry: '12/26'
          }
        }
        
        setBillingData(mockData)
      } catch (error) {
        console.error('Failed to fetch billing data:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchBillingData()
  }, [organization])

  const getTierIcon = (tier: string) => {
    switch (tier) {
      case 'enterprise':
        return <Crown className="h-5 w-5 text-purple-600" />
      case 'team':
        return <Users className="h-5 w-5 text-blue-600" />
      case 'pro':
        return <Zap className="h-5 w-5 text-orange-600" />
      default:
        return <Database className="h-5 w-5 text-gray-600" />
    }
  }

  const getUsageColor = (current: number, limit: number) => {
    const percentage = (current / limit) * 100
    if (percentage >= 90) return 'text-red-600'
    if (percentage >= 75) return 'text-amber-600'
    return 'text-green-600'
  }

  if (isLoading) {
    return <LoadingState text="Loading billing information..." />
  }

  if (!billingData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Billing Information Unavailable
          </CardTitle>
          <CardDescription>
            Unable to load billing information at this time.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current Plan */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {getTierIcon(billingData.currentPlan.tier)}
              Current Plan
            </div>
            <Badge variant="secondary" className="text-lg px-3 py-1">
              {billingData.currentPlan.name}
            </Badge>
          </CardTitle>
          <CardDescription>
            ${billingData.currentPlan.price}/{billingData.currentPlan.billingPeriod}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Plan Features */}
            <div>
              <h4 className="font-medium mb-2">Plan Features</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                {billingData.currentPlan.features.map((feature, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    <Check className="h-4 w-4 text-green-600" />
                    {feature}
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Quick Actions */}
            <div className="flex gap-3">
              <Button className="bg-[#007AFF] hover:bg-[#0056b3]">
                <TrendingUp className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
              <Button variant="outline">
                <ExternalLink className="h-4 w-4 mr-2" />
                Manage Billing
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Usage Overview
          </CardTitle>
          <CardDescription>
            Current usage for this billing period
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Members Usage */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4" />
                  <span className="font-medium">Team Members</span>
                </div>
                <span className={`text-sm font-medium ${getUsageColor(billingData.usage.members.current, billingData.usage.members.limit)}`}>
                  {billingData.usage.members.current} / {billingData.usage.members.limit}
                </span>
              </div>
              <Progress 
                value={(billingData.usage.members.current / billingData.usage.members.limit) * 100} 
                className="h-2"
              />
            </div>

            {/* Storage Usage */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span className="font-medium">Storage</span>
                </div>
                <span className={`text-sm font-medium ${getUsageColor(billingData.usage.storage.current, billingData.usage.storage.limit)}`}>
                  {billingData.usage.storage.current}GB / {billingData.usage.storage.limit}GB
                </span>
              </div>
              <Progress 
                value={(billingData.usage.storage.current / billingData.usage.storage.limit) * 100} 
                className="h-2"
              />
            </div>

            {/* API Calls Usage */}
            <div>
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  <span className="font-medium">API Calls</span>
                </div>
                <span className={`text-sm font-medium ${getUsageColor(billingData.usage.apiCalls.current, billingData.usage.apiCalls.limit)}`}>
                  {billingData.usage.apiCalls.current.toLocaleString()} / {billingData.usage.apiCalls.limit.toLocaleString()}
                </span>
              </div>
              <Progress 
                value={(billingData.usage.apiCalls.current / billingData.usage.apiCalls.limit) * 100} 
                className="h-2"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Billing Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Next Billing */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Next Billing
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Date</span>
                <span className="font-medium">
                  {new Date(billingData.nextBilling.date).toLocaleDateString()}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Amount</span>
                <span className="font-medium text-lg">
                  ${billingData.nextBilling.amount}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="h-5 w-5" />
              Payment Method
            </CardTitle>
          </CardHeader>
          <CardContent>
            {billingData.paymentMethod ? (
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Card</span>
                  <span className="font-medium">
                    {billingData.paymentMethod.type} ••••{billingData.paymentMethod.last4}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Expires</span>
                  <span className="font-medium">
                    {billingData.paymentMethod.expiry}
                  </span>
                </div>
                <Button variant="outline" size="sm" className="w-full mt-3">
                  Update Payment Method
                </Button>
              </div>
            ) : (
              <div className="text-center py-4">
                <CreditCard className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                <p className="text-sm text-muted-foreground mb-3">
                  No payment method on file
                </p>
                <Button size="sm" className="bg-[#007AFF] hover:bg-[#0056b3]">
                  Add Payment Method
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>

      {/* Billing History */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Billing History
            </div>
            <Button variant="outline" size="sm">
              <ExternalLink className="h-4 w-4 mr-2" />
              View All
            </Button>
          </CardTitle>
          <CardDescription>
            Recent billing transactions
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {/* Mock billing history entries */}
            {[
              { date: '2024-01-15', amount: 29, status: 'paid', description: 'Pro Plan - Monthly' },
              { date: '2023-12-15', amount: 29, status: 'paid', description: 'Pro Plan - Monthly' },
              { date: '2023-11-15', amount: 29, status: 'paid', description: 'Pro Plan - Monthly' },
            ].map((entry, index) => (
              <div key={index} className="flex items-center justify-between py-2 border-b last:border-b-0">
                <div>
                  <div className="font-medium">{entry.description}</div>
                  <div className="text-sm text-muted-foreground">
                    {new Date(entry.date).toLocaleDateString()}
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-medium">${entry.amount}</div>
                  <Badge variant="outline" className="text-green-600 border-green-600">
                    <Check className="h-3 w-3 mr-1" />
                    {entry.status}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
