"use client"

import * as React from "react"
import { Organization, OrganizationMembership } from "@clerk/nextjs/server"
import { useOrganization } from "@clerk/nextjs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { LoadingState } from "@/components/ui/LoadingStates"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  UserPlus, 
  Search, 
  MoreHorizontal, 
  Mail, 
  UserMinus,
  Shield,
  Crown,
  User,
  Check,
  X,
  Loader2
} from "lucide-react"

interface MemberManagementProps {
  organization: Organization
}

interface MemberData {
  id: string
  email: string
  firstName?: string
  lastName?: string
  imageUrl?: string
  role: string
  joinedAt: string
  lastActive?: string
  status: 'active' | 'invited' | 'suspended'
}

export function MemberManagement({ organization }: MemberManagementProps) {
  const { membershipList, invitationList } = useOrganization({
    membershipList: {},
    invitationList: {},
  })
  const [members, setMembers] = React.useState<MemberData[]>([])
  const [invitations, setInvitations] = React.useState<any[]>([])
  const [isLoading, setIsLoading] = React.useState(true)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [inviteEmail, setInviteEmail] = React.useState('')
  const [isInviting, setIsInviting] = React.useState(false)

  React.useEffect(() => {
    if (membershipList && invitationList) {
      // Process members
      const processedMembers: MemberData[] = membershipList.map((membership: any) => ({
        id: membership.publicUserData.userId,
        email: membership.publicUserData.identifier,
        firstName: membership.publicUserData.firstName,
        lastName: membership.publicUserData.lastName,
        imageUrl: membership.publicUserData.imageUrl,
        role: membership.role,
        joinedAt: membership.createdAt,
        status: 'active' as const
      }))

      // Process invitations
      const processedInvitations = invitationList.map((invitation: any) => ({
        id: invitation.id,
        email: invitation.emailAddress,
        role: invitation.role,
        invitedAt: invitation.createdAt,
        status: 'invited' as const
      }))

      setMembers(processedMembers)
      setInvitations(processedInvitations)
      setIsLoading(false)
    }
  }, [membershipList, invitationList])

  const handleInviteMember = async () => {
    if (!inviteEmail) return
    
    setIsInviting(true)
    try {
      // TODO: Implement organization.invite API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      setInviteEmail('')
    } catch (error) {
      console.error('Failed to invite member:', error)
    } finally {
      setIsInviting(false)
    }
  }

  const handleRevokeInvitation = async (invitationId: string) => {
    try {
      await new Promise(resolve => setTimeout(resolve, 500))
      setInvitations(prev => prev.filter(inv => inv.id !== invitationId))
    } catch (error) {
      console.error('Failed to revoke invitation:', error)
    }
  }

  const filteredMembers = members.filter(member =>
    member.email?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.firstName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    member.lastName?.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin':
        return <Crown className="h-4 w-4" />
      case 'manager':
        return <Shield className="h-4 w-4" />
      default:
        return <User className="h-4 w-4" />
    }
  }

  const getRoleBadgeVariant = (role: string) => {
    switch (role) {
      case 'admin':
        return 'default'
      case 'manager':
        return 'secondary'
      default:
        return 'outline'
    }
  }

  if (isLoading) {
    return <LoadingState text="Loading organization members..." />
  }

  return (
    <div className="space-y-6">
      {/* Invite New Member */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Invite New Member
          </CardTitle>
          <CardDescription>
            Invite new team members to join your organization
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Input
              placeholder="Enter email address"
              value={inviteEmail}
              onChange={(e) => setInviteEmail(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleInviteMember}
              disabled={!inviteEmail || isInviting}
              className="bg-[#007AFF] hover:bg-[#0056b3]"
            >
              {isInviting ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Inviting...
                </>
              ) : (
                <>
                  <Mail className="h-4 w-4 mr-2" />
                  Send Invite
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Members Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Organization Members</CardTitle>
              <CardDescription>
                {members.length} members • {invitations.length} pending invitations
              </CardDescription>
            </div>
            <div className="relative w-64">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search members..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Member</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Joined</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredMembers.map((member) => (
                  <TableRow key={member.id}>
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={member.imageUrl} alt={member.email} />
                          <AvatarFallback>
                            {member.firstName?.[0]}{member.lastName?.[0]}
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">
                            {member.firstName} {member.lastName}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {member.email}
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(member.role)} className="flex items-center gap-1 w-fit">
                        {getRoleIcon(member.role)}
                        {member.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(member.joinedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-green-600 border-green-600">
                        <Check className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>
                            Change Role
                          </DropdownMenuItem>
                          <DropdownMenuItem>
                            Send Message
                          </DropdownMenuItem>
                          <DropdownMenuItem 
                            className="text-red-600"
                          >
                            <UserMinus className="h-4 w-4 mr-2" />
                            Remove Member
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
                
                {/* Pending Invitations */}
                {invitations.map((invitation) => (
                  <TableRow key={invitation.id} className="bg-muted/50">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Mail className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                        <div>
                          <div className="font-medium">{invitation.email}</div>
                          <div className="text-sm text-muted-foreground">
                            Invitation pending
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant={getRoleBadgeVariant(invitation.role)} className="flex items-center gap-1 w-fit">
                        {getRoleIcon(invitation.role)}
                        {invitation.role}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date(invitation.invitedAt).toLocaleDateString()}
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="text-amber-600 border-amber-600">
                        <Mail className="h-3 w-3 mr-1" />
                        Invited
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleRevokeInvitation(invitation.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4 mr-1" />
                        Revoke
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
