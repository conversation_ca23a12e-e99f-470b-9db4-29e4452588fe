"use client"

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  Bar<PERSON>hart3, 
  TrendingUp,
  Clock,
  MapPin,
  Shield,
  AlertTriangle,
  CheckCircle,
  Globe,
  Monitor,
  Smartphone,
  Calendar,
  Activity
} from "lucide-react"

interface ActivityStats {
  totalLogins: number
  uniqueDevices: number
  uniqueLocations: number
  securityScore: number
  lastActivity: string
  weeklyActivity: {
    day: string
    logins: number
  }[]
  deviceBreakdown: {
    type: string
    count: number
    percentage: number
  }[]
  locationStats: {
    location: string
    count: number
    riskLevel: 'low' | 'medium' | 'high'
  }[]
  securityEvents: {
    type: string
    count: number
    trend: 'up' | 'down' | 'stable'
  }[]
}

export function ActivityDashboard() {
  const [stats, setStats] = React.useState<ActivityStats | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)

  React.useEffect(() => {
    const fetchActivityStats = async () => {
      try {
        // TODO: Implement activity dashboard API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockStats: ActivityStats = {
          totalLogins: 47,
          uniqueDevices: 3,
          uniqueLocations: 2,
          securityScore: 85,
          lastActivity: new Date().toISOString(),
          weeklyActivity: [
            { day: 'Mon', logins: 8 },
            { day: 'Tue', logins: 12 },
            { day: 'Wed', logins: 6 },
            { day: 'Thu', logins: 9 },
            { day: 'Fri', logins: 7 },
            { day: 'Sat', logins: 3 },
            { day: 'Sun', logins: 2 }
          ],
          deviceBreakdown: [
            { type: 'Desktop', count: 28, percentage: 60 },
            { type: 'Mobile', count: 15, percentage: 32 },
            { type: 'Tablet', count: 4, percentage: 8 }
          ],
          locationStats: [
            { location: 'San Francisco, CA', count: 42, riskLevel: 'low' },
            { location: 'New York, NY', count: 3, riskLevel: 'medium' },
            { location: 'Unknown Location', count: 2, riskLevel: 'high' }
          ],
          securityEvents: [
            { type: 'Successful Logins', count: 45, trend: 'stable' },
            { type: 'Failed Attempts', count: 2, trend: 'down' },
            { type: 'Profile Updates', count: 3, trend: 'up' },
            { type: '2FA Usage', count: 12, trend: 'up' }
          ]
        }
        
        setStats(mockStats)
      } catch (error) {
        console.error('Failed to fetch activity stats:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchActivityStats()
  }, [])

  const getSecurityScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600'
    if (score >= 60) return 'text-amber-600'
    return 'text-red-600'
  }

  const getRiskBadgeVariant = (riskLevel: string) => {
    switch (riskLevel) {
      case 'high':
        return 'destructive'
      case 'medium':
        return 'secondary'
      case 'low':
        return 'outline'
      default:
        return 'outline'
    }
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />
      case 'down':
        return <TrendingUp className="h-3 w-3 text-red-600 rotate-180" />
      default:
        return <div className="w-3 h-0.5 bg-gray-400" />
    }
  }

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType.toLowerCase()) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Monitor className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  if (isLoading) {
    return <LoadingState text="Loading activity dashboard..." />
  }

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-amber-500" />
            Data Unavailable
          </CardTitle>
          <CardDescription>
            Unable to load activity dashboard at this time.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  const maxLogins = Math.max(...stats.weeklyActivity.map(d => d.logins))

  return (
    <div className="space-y-6">
      {/* Security Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Score
          </CardTitle>
          <CardDescription>
            Overall security health of your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-6">
            <div className="flex-1">
              <div className={`text-4xl font-bold mb-2 ${getSecurityScoreColor(stats.securityScore)}`}>
                {stats.securityScore}%
              </div>
              <Progress 
                value={stats.securityScore} 
                className="h-3"
              />
              <div className="mt-2 text-sm text-muted-foreground">
                {stats.securityScore >= 80 && "Excellent security posture"}
                {stats.securityScore >= 60 && stats.securityScore < 80 && "Good security, room for improvement"}
                {stats.securityScore < 60 && "Security needs attention"}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground mb-1">Last Activity</div>
              <div className="flex items-center gap-1 text-sm">
                <Clock className="h-3 w-3" />
                {new Date(stats.lastActivity).toLocaleString()}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Activity className="h-5 w-5 text-blue-600" />
              <span className="font-medium">Total Logins</span>
            </div>
            <div className="text-2xl font-bold">{stats.totalLogins}</div>
            <div className="text-sm text-muted-foreground">Last 30 days</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Monitor className="h-5 w-5 text-purple-600" />
              <span className="font-medium">Devices</span>
            </div>
            <div className="text-2xl font-bold">{stats.uniqueDevices}</div>
            <div className="text-sm text-muted-foreground">Unique devices</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Globe className="h-5 w-5 text-green-600" />
              <span className="font-medium">Locations</span>
            </div>
            <div className="text-2xl font-bold">{stats.uniqueLocations}</div>
            <div className="text-sm text-muted-foreground">Access points</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-2">
              <Shield className="h-5 w-5 text-orange-600" />
              <span className="font-medium">Security</span>
            </div>
            <div className={`text-2xl font-bold ${getSecurityScoreColor(stats.securityScore)}`}>
              {stats.securityScore}%
            </div>
            <div className="text-sm text-muted-foreground">Health score</div>
          </CardContent>
        </Card>
      </div>

      {/* Weekly Activity Chart */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Weekly Activity
          </CardTitle>
          <CardDescription>
            Login activity over the past 7 days
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-end justify-between h-32 gap-2">
            {stats.weeklyActivity.map((day, index) => (
              <div key={index} className="flex flex-col items-center flex-1">
                <div 
                  className="w-full bg-blue-600 rounded-t"
                  style={{ 
                    height: `${(day.logins / maxLogins) * 100}%`,
                    minHeight: day.logins > 0 ? '8px' : '2px'
                  }}
                />
                <div className="text-sm font-medium text-center mt-2">{day.day}</div>
                <div className="text-xs text-muted-foreground">{day.logins}</div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Device and Location Breakdown */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="h-5 w-5" />
              Device Usage
            </CardTitle>
            <CardDescription>
              Breakdown by device type
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {stats.deviceBreakdown.map((device, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    {getDeviceIcon(device.type)}
                    <span className="font-medium">{device.type}</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="text-sm text-muted-foreground">
                      {device.count} logins
                    </div>
                    <div className="w-20">
                      <Progress value={device.percentage} className="h-2" />
                    </div>
                    <div className="text-sm font-medium w-10 text-right">
                      {device.percentage}%
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Location Stats */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="h-5 w-5" />
              Access Locations
            </CardTitle>
            <CardDescription>
              Login locations and risk assessment
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {stats.locationStats.map((location, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div className="font-medium">{location.location}</div>
                    <div className="text-sm text-muted-foreground">
                      {location.count} access{location.count !== 1 ? 'es' : ''}
                    </div>
                  </div>
                  <Badge variant={getRiskBadgeVariant(location.riskLevel)}>
                    {location.riskLevel} risk
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Security Events Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5" />
            Security Events Summary
          </CardTitle>
          <CardDescription>
            Recent security activity trends
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {stats.securityEvents.map((event, index) => (
              <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <div className="font-medium">{event.type}</div>
                  <div className="text-2xl font-bold">{event.count}</div>
                </div>
                <div className="flex items-center gap-2">
                  {getTrendIcon(event.trend)}
                  <span className="text-sm text-muted-foreground capitalize">
                    {event.trend}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
