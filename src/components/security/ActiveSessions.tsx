"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { LoadingState } from "@/components/ui/LoadingStates"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  Monitor, 
  Smartphone, 
  Tablet,
  MapPin,
  Clock,
  Shield,
  LogOut,
  AlertTriangle,
  Globe,
  Wifi
} from "lucide-react"

interface ActiveSession {
  id: string
  deviceType: 'desktop' | 'mobile' | 'tablet'
  deviceName: string
  browser: string
  ipAddress: string
  location: string
  country: string
  lastActive: string
  isCurrent: boolean
  isSecure: boolean
}

export function ActiveSessions() {
  const [sessions, setSessions] = React.useState<ActiveSession[]>([])
  const [isLoading, setIsLoading] = React.useState(true)
  const [terminatingSession, setTerminatingSession] = React.useState<string | null>(null)

  React.useEffect(() => {
    const fetchActiveSessions = async () => {
      try {
        // TODO: Implement sessions API
        // For now, simulate session data
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const mockSessions: ActiveSession[] = [
          {
            id: 'current',
            deviceType: 'desktop',
            deviceName: 'MacBook Pro',
            browser: 'Chrome 120.0',
            ipAddress: '*************',
            location: 'San Francisco, CA',
            country: 'United States',
            lastActive: new Date().toISOString(),
            isCurrent: true,
            isSecure: true
          },
          {
            id: 'session-2',
            deviceType: 'mobile',
            deviceName: 'iPhone 15',
            browser: 'Safari Mobile',
            ipAddress: '*********',
            location: 'San Francisco, CA',
            country: 'United States',
            lastActive: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            isCurrent: false,
            isSecure: true
          },
          {
            id: 'session-3',
            deviceType: 'desktop',
            deviceName: 'Unknown Device',
            browser: 'Firefox 119.0',
            ipAddress: '***********',
            location: 'New York, NY',
            country: 'United States',
            lastActive: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
            isCurrent: false,
            isSecure: false
          }
        ]
        
        setSessions(mockSessions)
      } catch (error) {
        console.error('Failed to fetch active sessions:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchActiveSessions()
  }, [])

  const getDeviceIcon = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return <Smartphone className="h-4 w-4" />
      case 'tablet':
        return <Tablet className="h-4 w-4" />
      default:
        return <Monitor className="h-4 w-4" />
    }
  }

  const getDeviceColor = (deviceType: string) => {
    switch (deviceType) {
      case 'mobile':
        return 'text-blue-600'
      case 'tablet':
        return 'text-purple-600'
      default:
        return 'text-gray-600'
    }
  }

  const formatLastActive = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
    
    if (diffInMinutes < 1) return 'Just now'
    if (diffInMinutes < 60) return `${diffInMinutes} minutes ago`
    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)} hours ago`
    return `${Math.floor(diffInMinutes / 1440)} days ago`
  }

  const handleTerminateSession = async (sessionId: string) => {
    if (sessionId === 'current') return // Can't terminate current session
    
    setTerminatingSession(sessionId)
    try {
      // TODO: Implement session termination API
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setSessions(prev => prev.filter(session => session.id !== sessionId))
    } catch (error) {
      console.error('Failed to terminate session:', error)
    } finally {
      setTerminatingSession(null)
    }
  }

  const handleTerminateAllSessions = async () => {
    try {
      // TODO: Implement terminate all sessions API
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      setSessions(prev => prev.filter(session => session.isCurrent))
    } catch (error) {
      console.error('Failed to terminate all sessions:', error)
    }
  }

  if (isLoading) {
    return <LoadingState text="Loading active sessions..." />
  }

  const activeSessions = sessions.filter(s => !s.isCurrent)
  const suspiciousSessions = sessions.filter(s => !s.isSecure)

  return (
    <div className="space-y-6">
      {/* Security Alert */}
      {suspiciousSessions.length > 0 && (
        <Card className="border-amber-200 bg-amber-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-amber-800">
              <AlertTriangle className="h-5 w-5" />
              Security Alert
            </CardTitle>
            <CardDescription className="text-amber-700">
              {suspiciousSessions.length} potentially suspicious session{suspiciousSessions.length > 1 ? 's' : ''} detected.
              Review and terminate any sessions you don't recognize.
            </CardDescription>
          </CardHeader>
        </Card>
      )}

      {/* Current Session */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Current Session
          </CardTitle>
          <CardDescription>
            This is your current active session
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sessions.filter(s => s.isCurrent).map((session) => (
            <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg bg-green-50 border-green-200">
              <div className="flex items-center gap-4">
                <div className={`${getDeviceColor(session.deviceType)}`}>
                  {getDeviceIcon(session.deviceType)}
                </div>
                <div>
                  <div className="font-medium">{session.deviceName}</div>
                  <div className="text-sm text-muted-foreground">
                    {session.browser} • {session.ipAddress}
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <MapPin className="h-3 w-3" />
                    {session.location}
                    <Globe className="h-3 w-3 ml-2" />
                    {session.country}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <Badge variant="outline" className="text-green-600 border-green-600 mb-2">
                  Current Session
                </Badge>
                <div className="text-sm text-muted-foreground">
                  <Clock className="h-3 w-3 inline mr-1" />
                  {formatLastActive(session.lastActive)}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      {/* Active Sessions */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Wifi className="h-5 w-5" />
                Active Sessions
              </CardTitle>
              <CardDescription>
                {activeSessions.length} other active session{activeSessions.length !== 1 ? 's' : ''}
              </CardDescription>
            </div>
            {activeSessions.length > 0 && (
              <Button
                variant="outline"
                onClick={handleTerminateAllSessions}
                className="text-red-600 border-red-200 hover:bg-red-50"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Terminate All
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {activeSessions.length === 0 ? (
            <div className="text-center py-8">
              <Shield className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="font-medium text-green-800 mb-2">All Clear!</h3>
              <p className="text-sm text-green-700">
                No other active sessions detected. Your account is secure.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Device</TableHead>
                    <TableHead>Location</TableHead>
                    <TableHead>Last Active</TableHead>
                    <TableHead>Security</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {activeSessions.map((session) => (
                    <TableRow key={session.id} className={!session.isSecure ? 'bg-red-50' : ''}>
                      <TableCell>
                        <div className="flex items-center gap-3">
                          <div className={`${getDeviceColor(session.deviceType)}`}>
                            {getDeviceIcon(session.deviceType)}
                          </div>
                          <div>
                            <div className="font-medium">{session.deviceName}</div>
                            <div className="text-sm text-muted-foreground">
                              {session.browser}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{session.location}</div>
                          <div className="text-sm text-muted-foreground">
                            {session.ipAddress} • {session.country}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <Clock className="h-3 w-3" />
                          {formatLastActive(session.lastActive)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={session.isSecure ? "outline" : "destructive"}
                          className={session.isSecure ? "text-green-600 border-green-600" : ""}
                        >
                          {session.isSecure ? (
                            <>
                              <Shield className="h-3 w-3 mr-1" />
                              Secure
                            </>
                          ) : (
                            <>
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Suspicious
                            </>
                          )}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleTerminateSession(session.id)}
                          disabled={terminatingSession === session.id}
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                        >
                          <LogOut className="h-4 w-4 mr-1" />
                          {terminatingSession === session.id ? 'Ending...' : 'End Session'}
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Session Security Tips */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Security Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
              <div>
                <div className="font-medium">Always sign out from public computers</div>
                <div className="text-muted-foreground">
                  Don't leave your session active on shared or public devices
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
              <div>
                <div className="font-medium">Review sessions regularly</div>
                <div className="text-muted-foreground">
                  Check this page periodically to ensure all sessions are yours
                </div>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2" />
              <div>
                <div className="font-medium">Terminate suspicious sessions immediately</div>
                <div className="text-muted-foreground">
                  If you see unfamiliar locations or devices, end those sessions right away
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
