"use client"

import * as React from "react"
import { useUser } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  Shield, 
  Smartphone, 
  Key,
  QrCode,
  Copy,
  Check,
  AlertTriangle,
  Lock,
  Unlock,
  Download,
  RefreshCw
} from "lucide-react"

interface TwoFactorSetupProps {
  onSetupComplete?: () => void
}

interface TwoFactorStatus {
  enabled: boolean
  method: 'none' | 'totp' | 'sms' | 'backup_codes'
  backupCodesGenerated: boolean
  lastUsed?: string
}

export function TwoFactorSetup({ onSetupComplete }: TwoFactorSetupProps) {
  const { user } = useUser()
  const [status, setStatus] = React.useState<TwoFactorStatus>({
    enabled: false,
    method: 'none',
    backupCodesGenerated: false
  })
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSetupMode, setIsSetupMode] = React.useState(false)
  const [qrCode, setQrCode] = React.useState<string>('')
  const [setupSecret, setSetupSecret] = React.useState<string>('')
  const [verificationCode, setVerificationCode] = React.useState('')
  const [backupCodes, setBackupCodes] = React.useState<string[]>([])
  const [isVerifying, setIsVerifying] = React.useState(false)
  const [setupStep, setSetupStep] = React.useState<'qr' | 'verify' | 'backup'>('qr')
  const [copied, setCopied] = React.useState(false)

  React.useEffect(() => {
    const fetchTwoFactorStatus = async () => {
      try {
        // TODO: Implement 2FA status API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        setStatus({
          enabled: false,
          method: 'none',
          backupCodesGenerated: false
        })
      } catch (error) {
        console.error('Failed to fetch 2FA status:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTwoFactorStatus()
  }, [])

  const handleStartSetup = async () => {
    setIsSetupMode(true)
    setSetupStep('qr')
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      setQrCode('mock-qr-code-data')
      setSetupSecret('JBSWY3DPEHPK3PXP')
    } catch (error) {
      console.error('Failed to start 2FA setup:', error)
    }
  }

  const handleVerifyCode = async () => {
    if (!verificationCode || verificationCode.length !== 6) return
    
    setIsVerifying(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      setSetupStep('backup')
      
      const codes = Array.from({ length: 10 }, () => 
        Math.random().toString(36).substring(2, 10).toUpperCase()
      )
      setBackupCodes(codes)
      
    } catch (error) {
      console.error('Failed to verify 2FA code:', error)
    } finally {
      setIsVerifying(false)
    }
  }

  const handleCompleteSetup = async () => {
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setStatus({
        enabled: true,
        method: 'totp',
        backupCodesGenerated: true,
        lastUsed: new Date().toISOString()
      })
      
      setIsSetupMode(false)
      onSetupComplete?.()
    } catch (error) {
      console.error('Failed to complete 2FA setup:', error)
    }
  }

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Failed to copy to clipboard:', error)
    }
  }

  if (isLoading) {
    return <LoadingState text="Loading two-factor authentication settings..." />
  }

  if (isSetupMode) {
    return (
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Two-Factor Authentication Setup
            </CardTitle>
            <CardDescription>
              Step {setupStep === 'qr' ? '1' : setupStep === 'verify' ? '2' : '3'} of 3
            </CardDescription>
          </CardHeader>
          <CardContent>
            {setupStep === 'qr' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium mb-2">Scan QR Code</h3>
                  <p className="text-muted-foreground mb-4">
                    Use your authenticator app to scan this QR code
                  </p>
                  
                  {/* Mock QR Code */}
                  <div className="inline-block p-6 bg-white border rounded-lg">
                    <QrCode className="h-32 w-32 text-gray-400" />
                  </div>
                </div>

                <Separator />

                <div>
                  <Label htmlFor="secret">Manual Entry Key</Label>
                  <div className="flex gap-2 mt-2">
                    <Input 
                      id="secret"
                      value={setupSecret}
                      readOnly
                      className="font-mono"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => copyToClipboard(setupSecret)}
                    >
                      {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
                    </Button>
                  </div>
                  <p className="text-sm text-muted-foreground mt-2">
                    Enter this key manually if you cannot scan the QR code
                  </p>
                </div>

                <Button 
                  onClick={() => setSetupStep('verify')}
                  className="w-full bg-[#007AFF] hover:bg-[#0056b3]"
                >
                  Continue to Verification
                </Button>
              </div>
            )}

            {setupStep === 'verify' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium mb-2">Verify Setup</h3>
                  <p className="text-muted-foreground">
                    Enter the 6-digit code from your authenticator app
                  </p>
                </div>

                <div className="max-w-xs mx-auto">
                  <Label htmlFor="verification">Verification Code</Label>
                  <Input
                    id="verification"
                    value={verificationCode}
                    onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                    placeholder="000000"
                    className="text-center text-lg font-mono"
                    maxLength={6}
                  />
                </div>

                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => setSetupStep('qr')}
                    className="flex-1"
                  >
                    Back
                  </Button>
                  <Button
                    onClick={handleVerifyCode}
                    disabled={verificationCode.length !== 6 || isVerifying}
                    className="flex-1 bg-[#007AFF] hover:bg-[#0056b3]"
                  >
                    {isVerifying ? 'Verifying...' : 'Verify'}
                  </Button>
                </div>
              </div>
            )}

            {setupStep === 'backup' && (
              <div className="space-y-6">
                <div className="text-center">
                  <h3 className="text-lg font-medium mb-2">Save Backup Codes</h3>
                  <p className="text-muted-foreground">
                    Store these backup codes in a safe place. You can use them to access your account if you lose your device.
                  </p>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <div className="grid grid-cols-2 gap-2 font-mono text-sm">
                    {backupCodes.map((code, index) => (
                      <div key={index} className="p-2 bg-white rounded border">
                        {code}
                      </div>
                    ))}
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => copyToClipboard(backupCodes.join('\n'))}
                    className="flex-1"
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy Codes
                  </Button>
                  <Button
                    onClick={() => {
                      const blob = new Blob([backupCodes.join('\n')], { type: 'text/plain' })
                      const url = URL.createObjectURL(blob)
                      const a = document.createElement('a')
                      a.href = url
                      a.download = 'backup-codes.txt'
                      a.click()
                    }}
                    variant="outline"
                    className="flex-1"
                  >
                    <Download className="h-4 w-4 mr-2" />
                    Download
                  </Button>
                </div>

                <Button
                  onClick={handleCompleteSetup}
                  className="w-full bg-[#007AFF] hover:bg-[#0056b3]"
                >
                  Complete Setup
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Two-Factor Authentication
            </div>
            <Badge variant={status.enabled ? "default" : "outline"}>
              {status.enabled ? (
                <>
                  <Lock className="h-3 w-3 mr-1" />
                  Enabled
                </>
              ) : (
                <>
                  <Unlock className="h-3 w-3 mr-1" />
                  Disabled
                </>
              )}
            </Badge>
          </CardTitle>
          <CardDescription>
            Add an extra layer of security to your account
          </CardDescription>
        </CardHeader>
        <CardContent>
          {status.enabled ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-5 w-5 text-green-600" />
                  <div>
                    <div className="font-medium">Authenticator App</div>
                    <div className="text-sm text-muted-foreground">
                      {status.lastUsed ? `Last used: ${new Date(status.lastUsed).toLocaleDateString()}` : 'Never used'}
                    </div>
                  </div>
                </div>
                <Badge variant="outline" className="text-green-600 border-green-600">
                  Active
                </Badge>
              </div>

              {status.backupCodesGenerated && (
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center gap-3">
                    <Key className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Backup Codes</div>
                      <div className="text-sm text-muted-foreground">
                        10 backup codes generated
                      </div>
                    </div>
                  </div>
                  <Button variant="outline" size="sm">
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Regenerate
                  </Button>
                </div>
              )}

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={handleDisable2FA}
                  className="text-red-600 border-red-200 hover:bg-red-50"
                >
                  Disable 2FA
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="flex items-start gap-3 p-4 bg-amber-50 border border-amber-200 rounded-lg">
                <AlertTriangle className="h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <div className="font-medium text-amber-800">Security Recommendation</div>
                  <div className="text-sm text-amber-700">
                    Enable two-factor authentication to significantly improve your account security.
                  </div>
                </div>
              </div>

              <div className="space-y-3">
                <h4 className="font-medium">Supported Methods:</h4>
                <div className="space-y-2">
                  <div className="flex items-center gap-3 p-3 border rounded-lg">
                    <Smartphone className="h-5 w-5 text-blue-600" />
                    <div>
                      <div className="font-medium">Authenticator App</div>
                      <div className="text-sm text-muted-foreground">
                        Google Authenticator, Authy, or similar apps
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <Button
                onClick={handleStartSetup}
                className="w-full bg-[#007AFF] hover:bg-[#0056b3]"
              >
                <Shield className="h-4 w-4 mr-2" />
                Enable Two-Factor Authentication
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
