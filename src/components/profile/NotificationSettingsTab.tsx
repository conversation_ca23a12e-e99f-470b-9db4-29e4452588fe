"use client"

import * as React from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  Bell,
  Mail,
  Monitor,
  Workflow,
  Users,
  Shield,
  Check,
  Loader2
} from "lucide-react"

interface NotificationSettings {
  email_notifications: boolean
  browser_notifications: boolean
  workflow_updates: boolean
  organization_updates: boolean
  security_alerts: boolean
}

const notificationCategories = [
  {
    id: 'email_notifications' as keyof NotificationSettings,
    title: 'Email Notifications',
    description: 'Receive notifications via email for important updates',
    icon: Mail
  },
  {
    id: 'browser_notifications' as keyof NotificationSettings,
    title: 'Browser Notifications',
    description: 'Show desktop notifications in your browser',
    icon: Monitor
  },
  {
    id: 'workflow_updates' as keyof NotificationSettings,
    title: 'Workflow Updates',
    description: 'Get notified about job postings, candidate updates, and workflow changes',
    icon: Workflow
  },
  {
    id: 'organization_updates' as keyof NotificationSettings,
    title: 'Organization Updates',
    description: 'Receive updates about your organization and team activities',
    icon: Users
  },
  {
    id: 'security_alerts' as keyof NotificationSettings,
    title: 'Security Alerts',
    description: 'Important security notifications and account alerts',
    icon: Shield,
    required: true
  }
]

export function NotificationSettingsTab() {
  const [settings, setSettings] = React.useState<NotificationSettings | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSaving, setIsSaving] = React.useState(false)
  const [hasChanges, setHasChanges] = React.useState(false)

  React.useEffect(() => {
    const fetchSettings = async () => {
      try {
        const response = await fetch('/api/test-enhanced-profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.profile?.notificationPreferences) {
            setSettings(data.profile.notificationPreferences)
          } else {
            // Default settings
            setSettings({
              email_notifications: true,
              browser_notifications: true,
              workflow_updates: true,
              organization_updates: true,
              security_alerts: true
            })
          }
        }
      } catch (error) {
        console.error('Failed to fetch notification settings:', error)
        // Set defaults on error
        setSettings({
          email_notifications: true,
          browser_notifications: true,
          workflow_updates: true,
          organization_updates: true,
          security_alerts: true
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchSettings()
  }, [])

  const handleToggle = (key: keyof NotificationSettings, value: boolean) => {
    if (!settings) return
    
    setSettings(prev => ({
      ...prev!,
      [key]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    if (!settings || !hasChanges) return

    setIsSaving(true)
    try {
      const response = await fetch('/api/test-enhanced-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_profile',
          notificationPreferences: settings
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        setHasChanges(false)
      } else {
        console.error('Failed to update notification settings:', result.error)
      }
    } catch (error) {
      console.error('Error updating notification settings:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const requestBrowserPermission = async () => {
    if ('Notification' in window) {
      const permission = await Notification.requestPermission()
      if (permission === 'granted') {
        handleToggle('browser_notifications', true)
      }
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading notification settings..." />
  }

  if (!settings) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Failed to load notification settings</p>
      </div>
    )
  }

  const browserPermission = typeof window !== 'undefined' && 'Notification' in window 
    ? Notification.permission 
    : 'default'

  return (
    <div className="space-y-6">
      {/* Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bell className="h-5 w-5" />
            Notification Preferences
          </CardTitle>
          <CardDescription>
            Configure how and when you want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {notificationCategories.map((category) => {
              const Icon = category.icon
              const isEnabled = settings[category.id]
              
              return (
                <div key={category.id} className="flex items-start space-x-4">
                  <div className="flex-shrink-0 mt-1">
                    <div className="flex h-10 w-10 items-center justify-center rounded-lg bg-accent">
                      <Icon className="h-5 w-5 text-accent-foreground" />
                    </div>
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor={category.id} className="text-base font-medium cursor-pointer">
                          {category.title}
                          {category.required && (
                            <span className="ml-1 text-xs text-red-500">*Required</span>
                          )}
                        </Label>
                        <p className="text-sm text-muted-foreground">
                          {category.description}
                        </p>
                      </div>
                      <Switch
                        id={category.id}
                        checked={isEnabled}
                        onCheckedChange={(checked) => handleToggle(category.id, checked)}
                        disabled={category.required && isEnabled}
                        className="ml-4"
                      />
                    </div>
                    
                    {/* Browser notification specific UI */}
                    {category.id === 'browser_notifications' && (
                      <div className="mt-2">
                        {browserPermission === 'default' && isEnabled && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={requestBrowserPermission}
                            className="text-xs"
                          >
                            Enable Browser Notifications
                          </Button>
                        )}
                        {browserPermission === 'denied' && (
                          <p className="text-xs text-yellow-600">
                            Browser notifications are blocked. Enable them in your browser settings.
                          </p>
                        )}
                        {browserPermission === 'granted' && isEnabled && (
                          <p className="text-xs text-green-600 flex items-center gap-1">
                            <Check className="h-3 w-3" />
                            Browser notifications enabled
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Email Frequency Settings */}
      <Card>
        <CardHeader>
          <CardTitle>Email Frequency</CardTitle>
          <CardDescription>
            Control how often you receive email notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-sm font-medium">Immediate</Label>
                <p className="text-xs text-muted-foreground">
                  Receive emails as events happen
                </p>
                <div className="space-y-1 text-xs">
                  <div>• Security alerts</div>
                  <div>• Urgent workflow updates</div>
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Daily Digest</Label>
                <p className="text-xs text-muted-foreground">
                  Daily summary at 9:00 AM
                </p>
                <div className="space-y-1 text-xs">
                  <div>• Workflow updates</div>
                  <div>• Organization updates</div>
                </div>
              </div>
              <div className="space-y-2">
                <Label className="text-sm font-medium">Weekly Summary</Label>
                <p className="text-xs text-muted-foreground">
                  Weekly report every Monday
                </p>
                <div className="space-y-1 text-xs">
                  <div>• Activity summary</div>
                  <div>• Performance metrics</div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quiet Hours */}
      <Card>
        <CardHeader>
          <CardTitle>Quiet Hours</CardTitle>
          <CardDescription>
            Set times when you don't want to receive notifications
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch id="quiet-hours" />
              <Label htmlFor="quiet-hours">Enable quiet hours</Label>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 opacity-50">
              <div className="space-y-2">
                <Label className="text-sm">From</Label>
                <select className="w-full p-2 border rounded text-sm" disabled>
                  <option>9:00 PM</option>
                </select>
              </div>
              <div className="space-y-2">
                <Label className="text-sm">To</Label>
                <select className="w-full p-2 border rounded text-sm" disabled>
                  <option>7:00 AM</option>
                </select>
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Security alerts will still be delivered during quiet hours
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex items-center justify-between pt-4 border-t">
        <p className="text-sm text-muted-foreground">
          {hasChanges ? 'You have unsaved changes' : 'All changes saved'}
        </p>
        <Button 
          onClick={handleSave} 
          disabled={!hasChanges || isSaving}
          className="min-w-[100px]"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : hasChanges ? (
            <>
              <Check className="h-4 w-4 mr-2" />
              Save Changes
            </>
          ) : (
            'Saved'
          )}
        </Button>
      </div>
    </div>
  )
}