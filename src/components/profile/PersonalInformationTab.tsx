"use client"

import * as React from "react"
import { useUser } from "@clerk/nextjs"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { LoadingState } from "@/components/ui/LoadingStates"
import { 
  Upload, 
  Check, 
  AlertCircle, 
  Loader2,
  MapPin,
  Phone,
  Linkedin,
  Globe,
  User
} from "lucide-react"

interface ProfileData {
  firstName: string
  lastName: string
  username: string
  displayName: string
  bio: string
  jobTitle: string
  phone: string
  location: string
  linkedinUrl: string
  websiteUrl: string
  profilePictureUrl: string
  profileCompleteness: number
  accountTier: string
}

export function PersonalInformationTab() {
  const { user, isLoaded } = useUser()
  const [profile, setProfile] = React.useState<ProfileData | null>(null)
  const [isLoading, setIsLoading] = React.useState(true)
  const [isSaving, setIsSaving] = React.useState(false)
  const [hasChanges, setHasChanges] = React.useState(false)

  // Fetch enhanced profile data
  React.useEffect(() => {
    const fetchProfile = async () => {
      try {
        const response = await fetch('/api/test-enhanced-profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.profile) {
            setProfile({
              firstName: data.profile.firstName || '',
              lastName: data.profile.lastName || '',
              username: data.profile.username || '',
              displayName: data.profile.displayName || '',
              bio: data.profile.bio || '',
              jobTitle: data.profile.jobTitle || '',
              phone: data.profile.phone || '',
              location: data.profile.location || '',
              linkedinUrl: data.profile.linkedinUrl || '',
              websiteUrl: data.profile.websiteUrl || '',
              profilePictureUrl: data.profile.profilePictureUrl || '',
              profileCompleteness: data.profile.profileCompleteness || 0,
              accountTier: data.profile.accountTier || 'free'
            })
          } else {
            // Initialize with Clerk data if no enhanced profile exists
            setProfile({
              firstName: user?.firstName || '',
              lastName: user?.lastName || '',
              username: user?.username || '',
              displayName: '',
              bio: '',
              jobTitle: '',
              phone: '',
              location: '',
              linkedinUrl: '',
              websiteUrl: '',
              profilePictureUrl: user?.imageUrl || '',
              profileCompleteness: 30,
              accountTier: 'free'
            })
          }
        }
      } catch (error) {
        console.error('Failed to fetch profile:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (isLoaded && user) {
      fetchProfile()
    }
  }, [isLoaded, user])

  const handleInputChange = (field: keyof ProfileData, value: string) => {
    if (!profile) return
    
    setProfile(prev => ({
      ...prev!,
      [field]: value
    }))
    setHasChanges(true)
  }

  const handleSave = async () => {
    if (!profile || !hasChanges) return

    setIsSaving(true)
    try {
      const response = await fetch('/api/test-enhanced-profile', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          action: 'update_profile',
          ...profile
        }),
      })

      const result = await response.json()
      
      if (result.success) {
        setHasChanges(false)
        
        // Update profile completeness if returned
        if (result.profile?.profileCompleteness !== undefined) {
          setProfile(prev => prev ? {
            ...prev,
            profileCompleteness: result.profile.profileCompleteness
          } : null)
        }
      } else {
        console.error('Failed to update profile:', result.error)
      }
    } catch (error) {
      console.error('Error updating profile:', error)
    } finally {
      setIsSaving(false)
    }
  }

  const getInitials = () => {
    if (profile?.firstName && profile?.lastName) {
      return `${profile.firstName[0]}${profile.lastName[0]}`.toUpperCase()
    }
    return profile?.firstName?.[0]?.toUpperCase() || 'U'
  }

  const getAccountTierColor = (tier: string) => {
    switch (tier) {
      case 'enterprise': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'team': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'pro': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  if (isLoading) {
    return <LoadingState message="Loading profile information..." />
  }

  if (!profile) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="text-center">
          <AlertCircle className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
          <p className="text-muted-foreground">Failed to load profile data</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Profile Completeness */}
      <div className="space-y-3">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-medium">Profile Completeness</h3>
          <span className="text-sm text-muted-foreground">{profile.profileCompleteness}%</span>
        </div>
        <Progress value={profile.profileCompleteness} className="h-2" />
        <p className="text-sm text-muted-foreground">
          Complete your profile to improve your professional presence and unlock features.
        </p>
      </div>

      <Separator />

      {/* Profile Picture & Basic Info */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Profile Picture & Basic Information</h3>
        
        <div className="flex items-start gap-6">
          <div className="space-y-2">
            <Avatar className="h-20 w-20">
              <AvatarImage src={profile.profilePictureUrl} alt={profile.firstName} />
              <AvatarFallback className="text-lg font-medium">
                {getInitials()}
              </AvatarFallback>
            </Avatar>
            <Button variant="outline" size="sm" className="w-20">
              <Upload className="h-3 w-3 mr-1" />
              Upload
            </Button>
          </div>

          <div className="flex-1 space-y-4">
            <div className="flex items-center gap-2">
              <Badge className={getAccountTierColor(profile.accountTier)}>
                {profile.accountTier.toUpperCase()}
              </Badge>
              <span className="text-sm text-muted-foreground">Account Tier</span>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">First Name</Label>
                <Input
                  id="firstName"
                  value={profile.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="Enter your first name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={profile.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Enter your last name"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  value={profile.username}
                  onChange={(e) => handleInputChange('username', e.target.value)}
                  placeholder="Choose a unique username"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="displayName">Display Name</Label>
                <Input
                  id="displayName"
                  value={profile.displayName}
                  onChange={(e) => handleInputChange('displayName', e.target.value)}
                  placeholder="How you'd like to be addressed"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Professional Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Professional Information</h3>
        
        <div className="space-y-2">
          <Label htmlFor="jobTitle">Job Title</Label>
          <Input
            id="jobTitle"
            value={profile.jobTitle}
            onChange={(e) => handleInputChange('jobTitle', e.target.value)}
            placeholder="e.g., Senior Software Engineer"
            className="flex items-center gap-2"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="bio">Professional Bio</Label>
          <Textarea
            id="bio"
            value={profile.bio}
            onChange={(e) => handleInputChange('bio', e.target.value)}
            placeholder="Tell us about your professional background and interests..."
            className="min-h-[100px]"
          />
          <p className="text-xs text-muted-foreground">
            This will be displayed on your public profile and to team members.
          </p>
        </div>
      </div>

      <Separator />

      {/* Contact Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Contact Information</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="phone" className="flex items-center gap-2">
              <Phone className="h-4 w-4" />
              Phone Number
            </Label>
            <Input
              id="phone"
              value={profile.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              placeholder="+****************"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="location" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              Location
            </Label>
            <Input
              id="location"
              value={profile.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder="San Francisco, CA"
            />
          </div>
        </div>
      </div>

      <Separator />

      {/* Social Links */}
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Social Links</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="linkedinUrl" className="flex items-center gap-2">
              <Linkedin className="h-4 w-4" />
              LinkedIn Profile
            </Label>
            <Input
              id="linkedinUrl"
              value={profile.linkedinUrl}
              onChange={(e) => handleInputChange('linkedinUrl', e.target.value)}
              placeholder="https://linkedin.com/in/username"
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor="websiteUrl" className="flex items-center gap-2">
              <Globe className="h-4 w-4" />
              Personal Website
            </Label>
            <Input
              id="websiteUrl"
              value={profile.websiteUrl}
              onChange={(e) => handleInputChange('websiteUrl', e.target.value)}
              placeholder="https://yourwebsite.com"
            />
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex items-center justify-between pt-4 border-t">
        <p className="text-sm text-muted-foreground">
          {hasChanges ? 'You have unsaved changes' : 'All changes saved'}
        </p>
        <Button 
          onClick={handleSave} 
          disabled={!hasChanges || isSaving}
          className="min-w-[100px]"
        >
          {isSaving ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Saving...
            </>
          ) : hasChanges ? (
            <>
              <Check className="h-4 w-4 mr-2" />
              Save Changes
            </>
          ) : (
            'Saved'
          )}
        </Button>
      </div>
    </div>
  )
}