"use client"

import * as React from "react"
import { useUser, useClerk } from "@clerk/nextjs"
import { useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { 
  User, 
  Settings, 
  CreditCard, 
  Shield, 
  Bell, 
  LogOut,
  ChevronDown,
  Building2,
  Moon,
  Sun,
  Monitor
} from "lucide-react"
import { useTheme } from "next-themes"

interface ProfileDropdownProps {
  isCollapsed?: boolean
  className?: string
}

interface UserProfileData {
  profileCompleteness: number
  accountTier: 'free' | 'pro' | 'team' | 'enterprise'
  organizationName?: string
  organizationRole?: string
}

export function ProfileDropdown({ isCollapsed = false, className }: ProfileDropdownProps) {
  const { user } = useUser()
  const { signOut } = useClerk()
  const router = useRouter()
  const { theme, setTheme } = useTheme()
  const [profileData, setProfileData] = React.useState<UserProfileData | null>(null)

  // Fetch enhanced profile data
  React.useEffect(() => {
    const fetchProfileData = async () => {
      try {
        const response = await fetch('/api/test-enhanced-profile')
        if (response.ok) {
          const data = await response.json()
          if (data.success && data.profile) {
            setProfileData({
              profileCompleteness: data.profile.profileCompleteness || 0,
              accountTier: data.profile.accountTier || 'free',
              organizationName: data.profile.organization?.name,
              organizationRole: data.profile.organizationRole
            })
          }
        }
      } catch (error) {
        console.error('Failed to fetch profile data:', error)
      }
    }

    if (user) {
      fetchProfileData()
    }
  }, [user])

  const handleSignOut = async () => {
    await signOut(() => router.push('/'))
  }

  const handleNavigation = (path: string) => {
    router.push(path)
  }

  const getAccountTierColor = (tier: string) => {
    switch (tier) {
      case 'enterprise': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200'
      case 'team': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200'
      case 'pro': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200'
    }
  }

  const getInitials = () => {
    if (user?.firstName && user?.lastName) {
      return `${user.firstName[0]}${user.lastName[0]}`.toUpperCase()
    }
    return user?.firstName?.[0]?.toUpperCase() || user?.emailAddresses[0]?.emailAddress[0]?.toUpperCase() || 'U'
  }

  const getCompletionColor = (percentage: number) => {
    if (percentage >= 80) return 'text-green-600 dark:text-green-400'
    if (percentage >= 50) return 'text-yellow-600 dark:text-yellow-400'
    return 'text-red-600 dark:text-red-400'
  }

  const ThemeToggle = () => (
    <DropdownMenuItem className="gap-2 cursor-pointer" onSelect={(e) => e.preventDefault()}>
      <div className="flex items-center gap-2 flex-1">
        {theme === 'dark' ? <Moon className="h-4 w-4" /> : 
         theme === 'light' ? <Sun className="h-4 w-4" /> : 
         <Monitor className="h-4 w-4" />}
        <span>Theme</span>
      </div>
      <div className="flex gap-1">
        <Button
          variant={theme === 'light' ? 'default' : 'outline'}
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => setTheme('light')}
        >
          <Sun className="h-3 w-3" />
        </Button>
        <Button
          variant={theme === 'dark' ? 'default' : 'outline'}
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => setTheme('dark')}
        >
          <Moon className="h-3 w-3" />
        </Button>
        <Button
          variant={theme === 'system' ? 'default' : 'outline'}
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => setTheme('system')}
        >
          <Monitor className="h-3 w-3" />
        </Button>
      </div>
    </DropdownMenuItem>
  )

  if (isCollapsed) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-10 w-10 rounded-full p-0">
            <Avatar className="h-8 w-8">
              <AvatarImage src={user?.imageUrl} alt={user?.firstName || 'User'} />
              <AvatarFallback className="text-xs font-medium">
                {getInitials()}
              </AvatarFallback>
            </Avatar>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent className="w-64" align="end" side="right">
          <DropdownMenuLabel className="p-0">
            <div className="flex items-center gap-3 p-3">
              <Avatar className="h-10 w-10">
                <AvatarImage src={user?.imageUrl} alt={user?.firstName || 'User'} />
                <AvatarFallback className="text-sm font-medium">
                  {getInitials()}
                </AvatarFallback>
              </Avatar>
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium truncate">
                  {user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'User'}
                </div>
                <div className="text-xs text-muted-foreground truncate">
                  {user?.primaryEmailAddress?.emailAddress}
                </div>
                <div className="flex items-center gap-2 mt-1">
                  {profileData && (
                    <>
                      <Badge className={cn("text-xs px-1.5 py-0.5", getAccountTierColor(profileData.accountTier))}>
                        {profileData.accountTier.toUpperCase()}
                      </Badge>
                      {profileData.profileCompleteness !== undefined && (
                        <span className={cn("text-xs font-medium", getCompletionColor(profileData.profileCompleteness))}>
                          {profileData.profileCompleteness}% complete
                        </span>
                      )}
                    </>
                  )}
                </div>
              </div>
            </div>
          </DropdownMenuLabel>
          <DropdownMenuSeparator />
          
          {profileData?.organizationName && (
            <>
              <div className="px-3 py-2">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Building2 className="h-3 w-3" />
                  <span>{profileData.organizationName}</span>
                  {profileData.organizationRole && (
                    <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                      {profileData.organizationRole}
                    </Badge>
                  )}
                </div>
              </div>
              <DropdownMenuSeparator />
            </>
          )}

          <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile')}>
            <User className="h-4 w-4" />
            <span>Profile Settings</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/account')}>
            <CreditCard className="h-4 w-4" />
            <span>Account & Billing</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/notifications')}>
            <Bell className="h-4 w-4" />
            <span>Notifications</span>
          </DropdownMenuItem>
          
          <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/security')}>
            <Shield className="h-4 w-4" />
            <span>Security & Privacy</span>
          </DropdownMenuItem>

          <DropdownMenuSeparator />
          
          <ThemeToggle />
          
          <DropdownMenuSeparator />
          
          <DropdownMenuItem className="gap-2 cursor-pointer text-red-600 focus:text-red-600" onClick={handleSignOut}>
            <LogOut className="h-4 w-4" />
            <span>Sign Out</span>
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className={cn("w-full justify-start gap-3 p-3 h-auto", className)}>
          <Avatar className="h-8 w-8">
            <AvatarImage src={user?.imageUrl} alt={user?.firstName || 'User'} />
            <AvatarFallback className="text-xs font-medium">
              {getInitials()}
            </AvatarFallback>
          </Avatar>
          <div className="flex-1 min-w-0 text-left">
            <div className="text-sm font-medium truncate">
              {user?.firstName ? `${user.firstName} ${user.lastName || ''}`.trim() : 'User'}
            </div>
            <div className="text-xs text-muted-foreground truncate">
              {user?.primaryEmailAddress?.emailAddress}
            </div>
            <div className="flex items-center gap-2 mt-0.5">
              {profileData && (
                <>
                  <Badge className={cn("text-xs px-1.5 py-0.5", getAccountTierColor(profileData.accountTier))}>
                    {profileData.accountTier.toUpperCase()}
                  </Badge>
                  {profileData.profileCompleteness !== undefined && (
                    <span className={cn("text-xs font-medium", getCompletionColor(profileData.profileCompleteness))}>
                      {profileData.profileCompleteness}% complete
                    </span>
                  )}
                </>
              )}
            </div>
          </div>
          <ChevronDown className="h-4 w-4 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-64" align="end">
        {profileData?.organizationName && (
          <>
            <div className="px-3 py-2">
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Building2 className="h-3 w-3" />
                <span>{profileData.organizationName}</span>
                {profileData.organizationRole && (
                  <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                    {profileData.organizationRole}
                  </Badge>
                )}
              </div>
            </div>
            <DropdownMenuSeparator />
          </>
        )}

        <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile')}>
          <User className="h-4 w-4" />
          <span>Profile Settings</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/account')}>
          <CreditCard className="h-4 w-4" />
          <span>Account & Billing</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/notifications')}>
          <Bell className="h-4 w-4" />
          <span>Notifications</span>
        </DropdownMenuItem>
        
        <DropdownMenuItem className="gap-2 cursor-pointer" onClick={() => handleNavigation('/dashboard/profile/security')}>
          <Shield className="h-4 w-4" />
          <span>Security & Privacy</span>
        </DropdownMenuItem>

        <DropdownMenuSeparator />
        
        <ThemeToggle />
        
        <DropdownMenuSeparator />
        
        <DropdownMenuItem className="gap-2 cursor-pointer text-red-600 focus:text-red-600" onClick={handleSignOut}>
          <LogOut className="h-4 w-4" />
          <span>Sign Out</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}