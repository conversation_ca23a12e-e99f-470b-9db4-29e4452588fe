/**
 * Clerk Billing Integration Demo
 * 
 * This demo shows how to use the new TalentHUB Clerk Billing integration
 * in your components. Copy these examples to implement subscription features.
 */

'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { Crown, Zap, Users, Database, BarChart3 } from 'lucide-react'

// Import the new billing components
import { 
  FeatureGate, 
  ProGate, 
  TeamGate, 
  APIGate, 
  UpgradeModal, 
  useUpgradeModal 
} from '@/components/billing'
import { useSubscription, useFeatureAccess, useUsageTracking } from '@/hooks/useSubscription'
import { CLERK_FEATURE_SLUGS } from '@/lib/billing/clerk-integration'

/**
 * Example 1: Basic Feature Gating
 */
export function BasicFeatureGatingExample() {
  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Basic Feature Gating Examples</h2>
      
      {/* Gate behind Pro plan */}
      <ProGate>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Advanced Analytics
            </CardTitle>
            <CardDescription>
              This content is only visible to Pro subscribers
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Detailed analytics and reporting features here...</p>
          </CardContent>
        </Card>
      </ProGate>

      {/* Gate behind Team plan */}
      <TeamGate>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Team Collaboration
            </CardTitle>
            <CardDescription>
              Team features require Team plan or higher
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Team collaboration tools and shared workspace...</p>
          </CardContent>
        </Card>
      </TeamGate>

      {/* Gate behind API access feature */}
      <APIGate>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5" />
              API Documentation
            </CardTitle>
            <CardDescription>
              API access requires Pro plan subscription
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Full REST API documentation and examples...</p>
          </CardContent>
        </Card>
      </APIGate>
    </div>
  )
}

/**
 * Example 2: Advanced Feature Gating with Custom Fallback
 */
export function AdvancedFeatureGatingExample() {
  const { openUpgradeModal } = useUpgradeModal()

  const customFallback = (
    <Card className="border-dashed border-2 border-gray-300">
      <CardContent className="p-6 text-center">
        <Crown className="h-12 w-12 text-gray-400 mx-auto mb-3" />
        <h3 className="font-semibold text-gray-900 mb-2">Premium Feature</h3>
        <p className="text-gray-600 mb-4">
          This advanced search feature is available with Pro plan
        </p>
        <Button onClick={() => openUpgradeModal('feature_access')}>
          <Zap className="h-4 w-4 mr-2" />
          Upgrade to Pro
        </Button>
      </CardContent>
    </Card>
  )

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Advanced Feature Gating</h2>
      
      <FeatureGate
        feature={CLERK_FEATURE_SLUGS.ADVANCED_SEARCH}
        fallback={customFallback}
      >
        <Card>
          <CardHeader>
            <CardTitle>Advanced Search Filters</CardTitle>
            <CardDescription>
              Boolean search, saved queries, and smart filters
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Advanced search interface would be here...</p>
          </CardContent>
        </Card>
      </FeatureGate>
    </div>
  )
}

/**
 * Example 3: Usage-Based Gating
 */
export function UsageGatingExample() {
  const { trackUsage, currentUsage } = useUsageTracking('record_creation')
  const { openUpgradeModal } = useUpgradeModal()
  const [isCreating, setIsCreating] = useState(false)

  const handleCreateRecord = async () => {
    setIsCreating(true)
    
    const result = await trackUsage(1, { 
      recordType: 'candidate',
      timestamp: new Date().toISOString()
    })
    
    if (result.blocked) {
      openUpgradeModal('usage_limit', undefined, 'records')
    } else if (result.success) {
      // Proceed with record creation
      console.log('Record created successfully')
    }
    
    setIsCreating(false)
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Usage-Based Gating</h2>
      
      <Card>
        <CardHeader>
          <CardTitle>Create New Candidate Record</CardTitle>
          <CardDescription>
            {currentUsage && (
              <span>
                Used {currentUsage.current} of {currentUsage.limit || '∞'} records this month
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {currentUsage && currentUsage.limit && (
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Monthly Usage</span>
                <span>{currentUsage.current} / {currentUsage.limit}</span>
              </div>
              <Progress value={currentUsage.percentage} />
            </div>
          )}
          
          <Button 
            onClick={handleCreateRecord} 
            disabled={isCreating}
            className="w-full"
          >
            {isCreating ? 'Creating...' : 'Create Candidate Record'}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Example 4: Subscription Status Display
 */
export function SubscriptionStatusExample() {
  const { subscription, usage, isLoading, openBillingPortal } = useSubscription()

  if (isLoading) {
    return <div>Loading subscription status...</div>
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Subscription Status</h2>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Current Plan
            <Badge variant={subscription?.isActive ? 'default' : 'secondary'}>
              {subscription?.talentHubTier.toUpperCase()}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {usage && Object.entries(usage).map(([key, data]) => (
              <div key={key} className="text-center">
                <p className="text-sm text-gray-600 capitalize">{key}</p>
                <p className="text-2xl font-bold">
                  {data.current}
                  {data.limit && (
                    <span className="text-sm text-gray-500">
                      / {data.limit}
                    </span>
                  )}
                </p>
                {data.limit && (
                  <Progress value={data.percentage} className="mt-1" />
                )}
              </div>
            ))}
          </div>
          
          <div className="flex gap-2">
            {subscription?.isActive && (
              <Button variant="outline" onClick={openBillingPortal}>
                Manage Billing
              </Button>
            )}
            {!subscription?.isActive && (
              <Button className="flex-1">
                <Crown className="h-4 w-4 mr-2" />
                Upgrade Plan
              </Button>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Example 5: Feature Access Checking
 */
export function FeatureAccessExample() {
  const { hasAccess } = useFeatureAccess(CLERK_FEATURE_SLUGS.API_ACCESS)
  const { subscription } = useSubscription()

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">Feature Access Checking</h2>
      
      <Card>
        <CardContent className="p-6">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>API Access</span>
              <Badge variant={hasAccess ? 'default' : 'secondary'}>
                {hasAccess ? 'Available' : 'Unavailable'}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span>Current Tier</span>
              <Badge variant="outline">
                {subscription?.talentHubTier.toUpperCase()}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between">
              <span>Active Features</span>
              <span className="text-sm text-gray-600">
                {subscription?.features.length || 0} features
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

/**
 * Main demo component
 */
export default function ClerkBillingDemo() {
  const { UpgradeModal } = useUpgradeModal()

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold mb-2">
          TalentHUB Clerk Billing Integration Demo
        </h1>
        <p className="text-gray-600">
          Examples of how to use the new subscription features
        </p>
      </div>

      <BasicFeatureGatingExample />
      <AdvancedFeatureGatingExample />
      <UsageGatingExample />
      <SubscriptionStatusExample />
      <FeatureAccessExample />

      {/* Upgrade Modal */}
      <UpgradeModal />
    </div>
  )
}
