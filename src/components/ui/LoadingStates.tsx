/**
 * Loading States Component
 * 
 * Professional loading animations and skeleton screens for TalentHUB.
 * Provides contextual loading states for different content types with
 * smooth transitions and accessibility features.
 * 
 * Features:
 * - Skeleton screens for list/grid content
 * - Progress bars for file uploads and sync operations
 * - Spinners for quick API calls
 * - Contextual loading messages
 * - Smooth animations and transitions
 * - ARIA labels for accessibility
 */

'use client'

import React from 'react'
import { Loader2, Upload, Sync, Search, Save, Users, FileText, Database } from 'lucide-react'
import { cn } from '@/lib/utils'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'

// Loading state types
export type LoadingType = 
  | 'spinner' 
  | 'skeleton' 
  | 'progress' 
  | 'overlay' 
  | 'inline' 
  | 'dots'

export type LoadingSize = 'sm' | 'md' | 'lg' | 'xl'

export type LoadingContext = 
  | 'page' 
  | 'component' 
  | 'form' 
  | 'search' 
  | 'upload' 
  | 'sync' 
  | 'save'
  | 'auth'

export interface LoadingStateProps {
  type?: LoadingType
  size?: LoadingSize
  message?: string
  context?: LoadingContext
  progress?: number
  className?: string
  showIcon?: boolean
  animated?: boolean
}

/**
 * Size configurations
 */
const SIZES = {
  sm: {
    spinner: 'w-4 h-4',
    text: 'text-sm',
    card: 'p-3',
    icon: 'w-4 h-4'
  },
  md: {
    spinner: 'w-6 h-6',
    text: 'text-base',
    card: 'p-4',
    icon: 'w-5 h-5'
  },
  lg: {
    spinner: 'w-8 h-8',
    text: 'text-lg',
    card: 'p-6',
    icon: 'w-6 h-6'
  },
  xl: {
    spinner: 'w-12 h-12',
    text: 'text-xl',
    card: 'p-8',
    icon: 'w-8 h-8'
  }
}

/**
 * Context-specific messages and icons
 */
const CONTEXT_CONFIG = {
  page: {
    icon: Database,
    messages: ['Loading page...', 'Getting your data...', 'Almost ready...'],
    defaultMessage: 'Loading...'
  },
  component: {
    icon: Loader2,
    messages: ['Loading...'],
    defaultMessage: 'Loading...'
  },
  form: {
    icon: Save,
    messages: ['Saving...', 'Processing...', 'Updating...'],
    defaultMessage: 'Processing...'
  },
  search: {
    icon: Search,
    messages: ['Searching...', 'Finding matches...', 'Analyzing results...'],
    defaultMessage: 'Searching...'
  },
  upload: {
    icon: Upload,
    messages: ['Uploading...', 'Processing file...', 'Almost done...'],
    defaultMessage: 'Uploading...'
  },
  sync: {
    icon: Sync,
    messages: ['Syncing...', 'Updating data...', 'Synchronizing...'],
    defaultMessage: 'Syncing...'
  },
  save: {
    icon: Save,
    messages: ['Saving...', 'Updating...', 'Processing changes...'],
    defaultMessage: 'Saving...'
  },
  auth: {
    icon: Users,
    messages: ['Authenticating...', 'Verifying credentials...', 'Signing in...'],
    defaultMessage: 'Authenticating...'
  }
}

/**
 * Main Loading State Component
 */
export function LoadingState({
  type = 'spinner',
  size = 'md',
  message,
  context = 'component',
  progress,
  className,
  showIcon = true,
  animated = true
}: LoadingStateProps) {
  const config = CONTEXT_CONFIG[context]
  const sizeConfig = SIZES[size]
  const Icon = config.icon
  
  const displayMessage = message || config.defaultMessage

  // Spinner loading
  if (type === 'spinner') {
    return (
      <div 
        className={cn(
          'flex items-center justify-center gap-3',
          sizeConfig.card,
          className
        )}
        role="status" 
        aria-label={displayMessage}
      >
        {showIcon && (
          <Icon 
            className={cn(
              sizeConfig.spinner, 
              'text-blue-600',
              animated && 'animate-spin'
            )} 
          />
        )}
        {displayMessage && (
          <span className={cn(sizeConfig.text, 'text-gray-600 font-medium')}>
            {displayMessage}
          </span>
        )}
      </div>
    )
  }

  // Progress bar loading
  if (type === 'progress') {
    return (
      <div 
        className={cn('space-y-3', sizeConfig.card, className)}
        role="progressbar"
        aria-valuenow={progress}
        aria-valuemin={0}
        aria-valuemax={100}
        aria-label={displayMessage}
      >
        {displayMessage && (
          <div className="flex items-center gap-2">
            {showIcon && (
              <Icon className={cn(sizeConfig.icon, 'text-blue-600')} />
            )}
            <span className={cn(sizeConfig.text, 'text-gray-700 font-medium')}>
              {displayMessage}
            </span>
            {typeof progress === 'number' && (
              <span className="text-sm text-gray-500 ml-auto">
                {Math.round(progress)}%
              </span>
            )}
          </div>
        )}
        <Progress 
          value={progress || 0} 
          className="h-2"
        />
      </div>
    )
  }

  // Overlay loading
  if (type === 'overlay') {
    return (
      <div 
        className={cn(
          'absolute inset-0 bg-white/80 backdrop-blur-sm',
          'flex items-center justify-center',
          'z-50',
          className
        )}
        role="status"
        aria-label={displayMessage}
      >
        <Card className="shadow-lg border-0 bg-white/90">
          <CardContent className={cn('flex items-center gap-3', sizeConfig.card)}>
            {showIcon && (
              <Icon 
                className={cn(
                  sizeConfig.spinner, 
                  'text-blue-600',
                  animated && 'animate-spin'
                )} 
              />
            )}
            {displayMessage && (
              <span className={cn(sizeConfig.text, 'text-gray-700 font-medium')}>
                {displayMessage}
              </span>
            )}
          </CardContent>
        </Card>
      </div>
    )
  }

  // Inline loading
  if (type === 'inline') {
    return (
      <span 
        className={cn('inline-flex items-center gap-2', className)}
        role="status"
        aria-label={displayMessage}
      >
        {showIcon && (
          <Icon 
            className={cn(
              sizeConfig.icon, 
              'text-blue-600',
              animated && 'animate-spin'
            )} 
          />
        )}
        {displayMessage && (
          <span className={cn(sizeConfig.text, 'text-gray-600')}>
            {displayMessage}
          </span>
        )}
      </span>
    )
  }

  // Dots loading
  if (type === 'dots') {
    return (
      <div 
        className={cn('flex items-center gap-2', className)}
        role="status"
        aria-label={displayMessage}
      >
        {displayMessage && (
          <span className={cn(sizeConfig.text, 'text-gray-600 mr-2')}>
            {displayMessage}
          </span>
        )}
        <div className="flex space-x-1">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className={cn(
                'bg-blue-600 rounded-full',
                size === 'sm' ? 'w-1 h-1' : 'w-2 h-2',
                animated && 'animate-bounce'
              )}
              style={{
                animationDelay: animated ? `${i * 0.1}s` : undefined,
                animationDuration: animated ? '0.6s' : undefined
              }}
            />
          ))}
        </div>
      </div>
    )
  }

  // Default to spinner
  return (
    <LoadingState 
      type="spinner" 
      size={size} 
      message={displayMessage} 
      context={context}
      className={className}
      showIcon={showIcon}
      animated={animated}
    />
  )
}

/**
 * Skeleton Loading Components
 */
export function SkeletonCard({ className }: { className?: string }) {
  return (
    <Card className={cn('p-4', className)}>
      <CardHeader className="p-0 space-y-3">
        <div className="flex items-center space-x-3">
          <div className="w-10 h-10 bg-gray-200 rounded-full animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0 pt-4 space-y-3">
        <div className="h-3 bg-gray-200 rounded animate-pulse" />
        <div className="h-3 bg-gray-200 rounded animate-pulse w-5/6" />
        <div className="h-3 bg-gray-200 rounded animate-pulse w-4/6" />
      </CardContent>
    </Card>
  )
}

export function SkeletonList({ 
  items = 3, 
  className 
}: { 
  items?: number
  className?: string 
}) {
  return (
    <div className={cn('space-y-3', className)}>
      {Array.from({ length: items }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3 p-3 border rounded-lg">
          <div className="w-8 h-8 bg-gray-200 rounded animate-pulse" />
          <div className="flex-1 space-y-2">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            <div className="h-3 bg-gray-200 rounded animate-pulse w-1/2" />
          </div>
          <div className="w-16 h-6 bg-gray-200 rounded animate-pulse" />
        </div>
      ))}
    </div>
  )
}

export function SkeletonTable({ 
  rows = 5, 
  columns = 4,
  className 
}: { 
  rows?: number
  columns?: number
  className?: string 
}) {
  return (
    <div className={cn('space-y-4', className)}>
      {/* Header */}
      <div className="flex space-x-4 p-3 border-b">
        {Array.from({ length: columns }).map((_, i) => (
          <div key={i} className="flex-1">
            <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
          </div>
        ))}
      </div>
      
      {/* Rows */}
      {Array.from({ length: rows }).map((_, rowIndex) => (
        <div key={rowIndex} className="flex space-x-4 p-3">
          {Array.from({ length: columns }).map((_, colIndex) => (
            <div key={colIndex} className="flex-1">
              <div className={cn(
                'h-3 bg-gray-200 rounded animate-pulse',
                colIndex === 0 ? 'w-full' : 'w-3/4'
              )} />
            </div>
          ))}
        </div>
      ))}
    </div>
  )
}

export function SkeletonText({ 
  lines = 3, 
  className 
}: { 
  lines?: number
  className?: string 
}) {
  return (
    <div className={cn('space-y-2', className)}>
      {Array.from({ length: lines }).map((_, i) => (
        <div 
          key={i} 
          className={cn(
            'h-3 bg-gray-200 rounded animate-pulse',
            i === lines - 1 ? 'w-3/4' : 'w-full'
          )} 
        />
      ))}
    </div>
  )
}

/**
 * Specialized Loading Components
 */

export function PageLoading({ message }: { message?: string }) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center p-4">
      <LoadingState 
        type="spinner"
        size="lg"
        context="page"
        message={message}
        className="bg-white rounded-lg shadow-sm border"
      />
    </div>
  )
}

export function FormLoading({ message }: { message?: string }) {
  return (
    <LoadingState 
      type="overlay"
      context="form"
      message={message}
    />
  )
}

export function SearchLoading({ message }: { message?: string }) {
  return (
    <LoadingState 
      type="dots"
      size="sm"
      context="search"
      message={message}
      className="justify-center py-8"
    />
  )
}

export function UploadLoading({ 
  progress, 
  message 
}: { 
  progress?: number
  message?: string 
}) {
  return (
    <LoadingState 
      type="progress"
      context="upload"
      progress={progress}
      message={message}
      className="max-w-md mx-auto"
    />
  )
}

export function SyncLoading({ 
  progress, 
  message 
}: { 
  progress?: number
  message?: string 
}) {
  return (
    <LoadingState 
      type={progress !== undefined ? "progress" : "spinner"}
      context="sync"
      progress={progress}
      message={message}
      size="sm"
      className="bg-blue-50 border border-blue-200 rounded-md p-3"
    />
  )
}

export function AuthLoading({ message }: { message?: string }) {
  return (
    <div className="flex items-center justify-center min-h-[200px]">
      <LoadingState 
        type="spinner"
        size="lg"
        context="auth"
        message={message}
      />
    </div>
  )
}

/**
 * Button Loading States
 */
export function ButtonLoading({ 
  children, 
  loading = false, 
  size = 'sm',
  ...props 
}: { 
  children: React.ReactNode
  loading?: boolean
  size?: LoadingSize
  [key: string]: any 
}) {
  if (!loading) {
    return children
  }

  return (
    <span className="inline-flex items-center gap-2">
      <Loader2 className={cn(SIZES[size].icon, 'animate-spin')} />
      {children}
    </span>
  )
}

/**
 * Card Loading Wrapper
 */
export function LoadingCard({ 
  loading = true, 
  children, 
  type = 'skeleton',
  className 
}: { 
  loading?: boolean
  children?: React.ReactNode
  type?: 'skeleton' | 'spinner'
  className?: string 
}) {
  if (!loading && children) {
    return <>{children}</>
  }

  if (type === 'skeleton') {
    return <SkeletonCard className={className} />
  }

  return (
    <Card className={cn('p-8', className)}>
      <LoadingState type="spinner" size="md" />
    </Card>
  )
}

/**
 * Conditional Loading Wrapper
 */
export function ConditionalLoading({ 
  loading, 
  children, 
  fallback,
  type = 'spinner',
  size = 'md',
  message 
}: { 
  loading: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
  type?: LoadingType
  size?: LoadingSize
  message?: string 
}) {
  if (loading) {
    return fallback || (
      <LoadingState 
        type={type} 
        size={size} 
        message={message} 
      />
    )
  }

  return <>{children}</>
}

/**
 * Grid Loading Layout
 */
export function GridLoading({ 
  items = 6, 
  columns = 3,
  className 
}: { 
  items?: number
  columns?: number
  className?: string 
}) {
  return (
    <div 
      className={cn(
        'grid gap-4',
        columns === 2 && 'grid-cols-1 md:grid-cols-2',
        columns === 3 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
        columns === 4 && 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
        className
      )}
    >
      {Array.from({ length: items }).map((_, i) => (
        <SkeletonCard key={i} />
      ))}
    </div>
  )
}

/**
 * Error state with retry loading
 */
export function ErrorLoadingState({ 
  onRetry, 
  isRetrying = false,
  message = "Something went wrong"
}: { 
  onRetry?: () => void
  isRetrying?: boolean
  message?: string 
}) {
  return (
    <div className="text-center py-8">
      <p className="text-gray-600 mb-4">{message}</p>
      {onRetry && (
        <button
          onClick={onRetry}
          disabled={isRetrying}
          className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {isRetrying ? (
            <>
              <Loader2 className="w-4 h-4 animate-spin" />
              Retrying...
            </>
          ) : (
            <>
              <Sync className="w-4 h-4" />
              Try Again
            </>
          )}
        </button>
      )}
    </div>
  )
}

export default LoadingState