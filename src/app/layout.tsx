import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  SignInButton,
  SignUpButton,
  SignedIn,
  SignedOut,
  UserButton,
} from "@clerk/nextjs";
import { Toaster } from "sonner";
import "./globals.css";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "TalentHUB - Multi-Tier Hiring Solutions",
  description: "B2B SaaS platform for recruitment and bench sales processes with timezone management",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={inter.className}>
          {children}
          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}