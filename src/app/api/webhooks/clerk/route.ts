import { NextRequest, NextResponse } from 'next/server'
import { headers } from 'next/headers'
import { 
  verifyClerkWebhook, 
  processUserEvent, 
  processOrganizationEvent,
  ClerkWebhookEvent 
} from '@/lib/services/webhook-verification'

/**
 * Clerk Webhook Handler
 * 
 * Handles incoming webhooks from <PERSON> to maintain sync between
 * Clerk authentication and Supabase database. Processes user
 * lifecycle events and organization membership changes.
 * 
 * Supported events:
 * - user.created → Create user_preferences record
 * - user.updated → Sync profile changes
 * - session.ended → Update last_active timestamp
 * - organizationMembership.created → Associate user with org
 * - organizationMembership.deleted → Remove org association
 * - organizationMembership.updated → Update user role
 */

export async function POST(request: NextRequest) {
  try {
    // Get webhook secret from environment
    const webhookSecret = process.env.CLERK_WEBHOOK_SECRET
    if (!webhookSecret) {
      console.error('CLERK_WEBHOOK_SECRET not configured')
      return NextResponse.json(
        { error: 'Webhook secret not configured' },
        { status: 500 }
      )
    }

    // Get signature from headers
    const headersList = headers()
    const signature = headersList.get('svix-signature')
    if (!signature) {
      console.error('No signature provided')
      return NextResponse.json(
        { error: 'No signature provided' },
        { status: 401 }
      )
    }

    // Get request body
    const body = await request.text()
    if (!body) {
      console.error('No body provided')
      return NextResponse.json(
        { error: 'No body provided' },
        { status: 400 }
      )
    }

    // Verify webhook signature
    if (!verifyClerkWebhook(signature, body, webhookSecret)) {
      console.error('Webhook signature verification failed')
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse the webhook event
    let event: ClerkWebhookEvent
    try {
      event = JSON.parse(body)
    } catch (error) {
      console.error('Failed to parse webhook body:', error)
      return NextResponse.json(
        { error: 'Invalid JSON body' },
        { status: 400 }
      )
    }

    // Log the event for debugging (remove in production)
    console.log('Received Clerk webhook:', {
      type: event.type,
      object: event.object,
      timestamp: new Date().toISOString()
    })

    // Process the event based on type
    let result: { success: boolean; error?: string }

    if (event.type.startsWith('user.') || event.type.startsWith('session.')) {
      result = await processUserEvent(event.type, event.data)
    } else if (event.type.startsWith('organizationMembership.')) {
      result = await processOrganizationEvent(event.type, event.data)
    } else {
      // Log unhandled event types for future implementation
      console.log('Unhandled webhook event type:', event.type)
      result = { success: true }
    }

    if (!result.success) {
      console.error('Failed to process webhook event:', {
        type: event.type,
        error: result.error
      })
      return NextResponse.json(
        { error: 'Failed to process event', details: result.error },
        { status: 500 }
      )
    }

    // Return success response
    return NextResponse.json(
      { 
        success: true, 
        message: 'Webhook processed successfully',
        eventType: event.type
      },
      { status: 200 }
    )

  } catch (error) {
    console.error('Webhook handler error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

// Handle GET requests (for webhook endpoint verification)
export async function GET() {
  return NextResponse.json(
    { 
      message: 'TalentHUB Clerk Webhook Endpoint',
      status: 'active',
      timestamp: new Date().toISOString()
    },
    { status: 200 }
  )
}