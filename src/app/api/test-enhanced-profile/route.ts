import { NextRequest, NextResponse } from 'next/server'
import { auth } from '@clerk/nextjs/server'
import { 
  getEnhancedUserProfile,
  updateEnhancedUserProfile,
  createUserPreferences
} from '@/lib/services/user-preferences'
import { trackUserActivity, ACTIVITY_TYPES } from '@/lib/services/activity-tracking'

/**
 * Enhanced Profile Test Endpoint
 * 
 * Test endpoint for the enhanced profile system.
 * Tests profile creation, updates, and activity tracking.
 */

export async function GET() {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    // Get enhanced profile
    const profile = await getEnhancedUserProfile(userId)
    
    if (!profile) {
      return NextResponse.json(
        { 
          message: 'No enhanced profile found',
          userId,
          suggestion: 'Create profile first using POST request'
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      profile,
      message: 'Enhanced profile retrieved successfully'
    })

  } catch (error) {
    console.error('Enhanced profile test GET error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { userId } = auth()
    
    if (!userId) {
      return NextResponse.json(
        { error: 'Not authenticated' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { action, ...data } = body

    switch (action) {
      case 'create_profile':
        const createResult = await createUserPreferences({
          userId,
          firstName: data.firstName || 'Test',
          lastName: data.lastName || 'User',
          username: data.username || `testuser_${Date.now()}`,
          email: data.email || '<EMAIL>'
        })

        if (createResult.success) {
          await trackUserActivity(
            userId,
            ACTIVITY_TYPES.PROFILE_UPDATED,
            { source: 'test_endpoint', action: 'create_profile' },
            request
          )
        }

        return NextResponse.json(createResult)

      case 'update_profile':
        const updateResult = await updateEnhancedUserProfile(
          userId,
          data,
          request
        )

        return NextResponse.json(updateResult)

      case 'test_activity':
        const activityResult = await trackUserActivity(
          userId,
          ACTIVITY_TYPES.PROFILE_UPDATED,
          { 
            source: 'test_endpoint', 
            test_data: data,
            timestamp: new Date().toISOString()
          },
          request
        )

        return NextResponse.json({
          success: activityResult.success,
          message: 'Activity tracked successfully',
          error: activityResult.error
        })

      default:
        return NextResponse.json(
          { error: 'Invalid action. Use: create_profile, update_profile, or test_activity' },
          { status: 400 }
        )
    }

  } catch (error) {
    console.error('Enhanced profile test POST error:', error)
    return NextResponse.json(
      { 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}