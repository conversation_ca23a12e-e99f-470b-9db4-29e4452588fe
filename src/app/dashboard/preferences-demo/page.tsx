import { PreferencesModal } from '@/components/preferences'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Settings, User, Palette, Building } from 'lucide-react'

/**
 * Demo page for testing the preferences modal
 */
export default function PreferencesDemoPage() {
  return (
    <div className="container max-w-4xl mx-auto py-8 space-y-8">
      {/* Page Header */}
      <div className="space-y-2">
        <h1 className="text-3xl font-bold">Preferences Modal Demo</h1>
        <p className="text-muted-foreground">
          Test the enterprise-grade preferences modal with real-time validation and professional B2B design.
        </p>
      </div>

      {/* Demo Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {/* Profile Management */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Profile Management
            </CardTitle>
            <CardDescription>
              Real-time username validation with smart suggestions
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• First & Last Name</li>
              <li>• Username availability checking</li>
              <li>• Smart username suggestions</li>
              <li>• Optional display name</li>
              <li>• Live profile preview</li>
            </ul>
          </CardContent>
        </Card>

        {/* Display Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Display Preferences
            </CardTitle>
            <CardDescription>
              Theme selection with live preview functionality
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Light/Dark/System themes</li>
              <li>• Live theme preview</li>
              <li>• Smart timezone picker</li>
              <li>• Process focus selection</li>
              <li>• Workflow previews</li>
            </ul>
          </CardContent>
        </Card>

        {/* Account Information */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Account & Organization
            </CardTitle>
            <CardDescription>
              Subscription tiers and organization association
            </CardDescription>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-1 text-muted-foreground">
              <li>• Plan features overview</li>
              <li>• Upgrade prompts</li>
              <li>• Organization details</li>
              <li>• Domain-based association</li>
              <li>• Subscription status</li>
            </ul>
          </CardContent>
        </Card>
      </div>

      {/* Demo Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Try the Preferences Modal</CardTitle>
          <CardDescription>
            Click the button below to open the modal and test all features including real-time validation,
            theme previews, and responsive design.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            {/* Default Button */}
            <PreferencesModal>
              <Button>
                <Settings className="h-4 w-4 mr-2" />
                Open Preferences
              </Button>
            </PreferencesModal>

            {/* Icon Button */}
            <PreferencesModal>
              <Button variant="outline" size="icon">
                <Settings className="h-4 w-4" />
              </Button>
            </PreferencesModal>

            {/* Link Style Button */}
            <PreferencesModal>
              <Button variant="link">
                Edit Profile Settings
              </Button>
            </PreferencesModal>
          </div>

          <div className="rounded-lg bg-muted p-4 text-sm">
            <h4 className="font-medium mb-2">Features to Test:</h4>
            <ul className="space-y-1 text-muted-foreground">
              <li>• Type a username to see real-time validation</li>
              <li>• Try switching themes to see the live preview</li>
              <li>• Search for timezones in the timezone selector</li>
              <li>• Change your process focus to see workflow previews</li>
              <li>• View your account features and organization info</li>
              <li>• Test the mobile responsive design</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      {/* Technical Details */}
      <Card>
        <CardHeader>
          <CardTitle>Technical Implementation</CardTitle>
          <CardDescription>
            Built with enterprise-grade patterns and best practices
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
            <div>
              <h4 className="font-medium mb-2">Frontend Features</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Real-time form validation</li>
                <li>• Debounced username checking</li>
                <li>• Optimistic UI updates</li>
                <li>• Comprehensive error handling</li>
                <li>• Accessibility compliance</li>
                <li>• Mobile-responsive design</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium mb-2">Backend Integration</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Supabase database integration</li>
                <li>• Clerk authentication sync</li>
                <li>• Row-level security policies</li>
                <li>• Global username uniqueness</li>
                <li>• Organization domain detection</li>
                <li>• Automated audit trails</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}