"use client"

import * as React from "react"
import { useUser, useOrganization } from "@clerk/nextjs"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { LoadingState } from "@/components/ui/LoadingStates"
import { PersonalInformationTab } from "@/components/profile/PersonalInformationTab"
import { AccountSettingsTab } from "@/components/profile/AccountSettingsTab"
import { NotificationSettingsTab } from "@/components/profile/NotificationSettingsTab"
import { SecuritySettingsTab } from "@/components/profile/SecuritySettingsTab"
import { 
  User, 
  Settings, 
  Bell, 
  Shield,
  Building2
} from "lucide-react"

export default function ProfilePage() {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded, membership } = useOrganization()
  const [activeTab, setActiveTab] = React.useState("personal")

  if (!userLoaded || !orgLoaded) {
    return (
      <div className="p-6">
        <LoadingState text="Loading profile..." />
      </div>
    )
  }

  if (!user) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              Please sign in to access your profile settings.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  // Check if user is admin for organization tab
  const isOrgAdmin = organization && membership && membership.role === 'admin'

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Profile Settings</h1>
        <p className="text-muted-foreground">
          Manage your personal information, account settings, and preferences
        </p>
      </div>

      {/* Profile Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className={`grid w-full ${isOrgAdmin ? 'grid-cols-5' : 'grid-cols-4'}`}>
          <TabsTrigger value="personal" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Personal
          </TabsTrigger>
          <TabsTrigger value="account" className="flex items-center gap-2">
            <Settings className="h-4 w-4" />
            Account
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="h-4 w-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Security
          </TabsTrigger>
          {isOrgAdmin && (
            <TabsTrigger value="organization" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Organization
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="personal">
          <PersonalInformationTab />
        </TabsContent>

        <TabsContent value="account">
          <AccountSettingsTab />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettingsTab />
        </TabsContent>

        <TabsContent value="security">
          <SecuritySettingsTab />
        </TabsContent>

        {isOrgAdmin && (
          <TabsContent value="organization">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5" />
                  Organization Management
                </CardTitle>
                <CardDescription>
                  Access comprehensive organization settings and member management.
                  <br />
                  <a 
                    href="/dashboard/profile/organization" 
                    className="text-[#007AFF] hover:underline font-medium"
                  >
                    Go to Organization Settings →
                  </a>
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Organization Profile</h4>
                    <p className="text-sm text-muted-foreground">
                      Update organization information, logo, and settings
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Member Management</h4>
                    <p className="text-sm text-muted-foreground">
                      Invite, remove, and manage organization members
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Role Assignment</h4>
                    <p className="text-sm text-muted-foreground">
                      Configure roles and permissions for team members
                    </p>
                  </div>
                  <div className="p-4 border rounded-lg">
                    <h4 className="font-medium mb-2">Billing & Usage</h4>
                    <p className="text-sm text-muted-foreground">
                      View subscription details and usage statistics
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
