"use client"

import * as React from "react"
import { useUser, useOrganization } from "@clerk/nextjs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { LoadingState } from "@/components/ui/LoadingStates"
import { OrganizationProfile, MemberManagement, RoleAssignment, OrganizationBilling } from "@/components/org"
import { 
  Building2, 
  Users, 
  Shield, 
  CreditCard, 
  AlertTriangle
} from "lucide-react"

export default function OrganizationSettingsPage() {
  const { user, isLoaded: userLoaded } = useUser()
  const { organization, isLoaded: orgLoaded, membership } = useOrganization()
  const [isAdmin, setIsAdmin] = React.useState(false)

  React.useEffect(() => {
    if (orgLoaded && membership) {
      setIsAdmin(membership.role === 'admin')
    }
  }, [orgLoaded, membership])

  if (!userLoaded || !orgLoaded) {
    return (
      <div className="p-6">
        <LoadingState text="Loading organization settings..." />
      </div>
    )
  }

  if (!organization) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              No Organization
            </CardTitle>
            <CardDescription>
              You are not currently a member of any organization. Join or create an organization to access these settings.
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  if (!isAdmin) {
    return (
      <div className="p-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-amber-500" />
              Access Restricted
            </CardTitle>
            <CardDescription>
              Only organization administrators can access organization settings. 
              Your current role is: <Badge variant="secondary">{membership?.role}</Badge>
            </CardDescription>
          </CardHeader>
        </Card>
      </div>
    )
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold">Organization Settings</h1>
        <p className="text-muted-foreground">
          Manage your organization profile, members, and settings
        </p>
      </div>

      {/* Organization Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {organization.name}
          </CardTitle>
          <CardDescription>
            Organization ID: {organization.id} • Members: {organization.membersCount}
          </CardDescription>
        </CardHeader>
      </Card>

      {/* Tabs */}
      <Tabs defaultValue="profile" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Profile
          </TabsTrigger>
          <TabsTrigger value="members" className="flex items-center gap-2">
            <Users className="h-4 w-4" />
            Members
          </TabsTrigger>
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="billing" className="flex items-center gap-2">
            <CreditCard className="h-4 w-4" />
            Billing
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile">
          <OrganizationProfile organization={organization} />
        </TabsContent>

        <TabsContent value="members">
          <MemberManagement organization={organization} />
        </TabsContent>

        <TabsContent value="roles">
          <RoleAssignment organization={organization} />
        </TabsContent>

        <TabsContent value="billing">
          <OrganizationBilling organization={organization} />
        </TabsContent>
      </Tabs>
    </div>
  )
}
