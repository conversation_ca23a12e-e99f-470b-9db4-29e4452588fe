import { createClient } from '@/lib/supabase'
import { detectOrganizationByEmail } from './domain-detection'
import { 
  validateOrganizationAccess, 
  getUpgradeRequirement,
  TIER_ACCESS,
  type AccountTier 
} from './tier-gating'

/**
 * User Preferences Service
 * 
 * Manages user preferences, profile information, and organization associations.
 * Integrates with Clerk authentication and Supabase database.
 * 
 * Features:
 * - User preferences CRUD operations
 * - Organization association logic
 * - Profile management
 * - Account tier handling
 * - Theme and timezone preferences
 */

// Types for user preferences
export interface UserPreferences {
  id: string
  userId: string
  orgId?: string
  isOrgMember: boolean
  firstName: string
  lastName: string
  username: string
  displayName?: string
  timezone: string
  theme: 'light' | 'dark' | 'system'
  processContext: 'recruitment' | 'bench_sales' | 'both'
  accountTier: 'free' | 'pro' | 'team' | 'enterprise'
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid'
  featuresEnabled: Record<string, boolean>
  createdAt: string
  updatedAt: string
}

export interface UserPreferencesWithOrg extends UserPreferences {
  organization?: {
    id: string
    name: string
    slug: string
    type: string
  }
}

export interface CreateUserPreferencesInput {
  userId: string
  firstName: string
  lastName: string
  username: string
  displayName?: string
  timezone?: string
  theme?: 'light' | 'dark' | 'system'
  processContext?: 'recruitment' | 'bench_sales' | 'both'
  email?: string // For organization detection
}

export interface UpdateUserPreferencesInput {
  firstName?: string
  lastName?: string
  username?: string
  displayName?: string
  timezone?: string
  theme?: 'light' | 'dark' | 'system'
  processContext?: 'recruitment' | 'bench_sales' | 'both'
  featuresEnabled?: Record<string, boolean>
}

/**
 * Gets user preferences by user ID
 */
export async function getUserPreferences(userId: string): Promise<UserPreferences | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      console.error('Error fetching user preferences:', error)
      return null
    }
    
    return transformDatabaseToPreferences(data)
    
  } catch (error) {
    console.error('Get user preferences failed:', error)
    return null
  }
}

/**
 * Creates new user preferences with optional organization detection
 */
export async function createUserPreferences(
  input: CreateUserPreferencesInput
): Promise<{ success: boolean; preferences?: UserPreferences; error?: string }> {
  try {
    const supabase = createClient()
    
    // Detect organization if email is provided
    let orgId: string | undefined
    let isOrgMember = false
    
    if (input.email) {
      const orgDetection = await detectOrganizationByEmail(input.email)
      if (orgDetection.found && orgDetection.organization?.autoJoinEnabled) {
        orgId = orgDetection.organization.id
        isOrgMember = false // Domain-based association, not formal membership
      }
    }
    
    const preferencesData = {
      user_id: input.userId,
      org_id: orgId,
      is_org_member: isOrgMember,
      first_name: input.firstName.trim(),
      last_name: input.lastName.trim(),
      username: input.username.trim(),
      display_name: input.displayName?.trim() || null,
      timezone: input.timezone || 'America/Chicago',
      theme: input.theme || 'system',
      process_context: input.processContext || 'both',
      account_tier: 'free',
      subscription_status: 'active',
      features_enabled: {}
    }
    
    const { data, error } = await supabase
      .from('user_preferences')
      .insert(preferencesData)
      .select()
      .single()
    
    if (error) {
      console.error('Error creating user preferences:', error)
      
      // Provide specific error messages for common issues
      if (error.code === '23505' && error.message.includes('username')) {
        return {
          success: false,
          error: 'Username is already taken'
        }
      }
      
      return {
        success: false,
        error: 'Failed to create user preferences'
      }
    }
    
    return {
      success: true,
      preferences: transformDatabaseToPreferences(data)
    }
    
  } catch (error) {
    console.error('Create user preferences failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Updates existing user preferences
 */
export async function updateUserPreferences(
  userId: string,
  updates: UpdateUserPreferencesInput
): Promise<{ success: boolean; preferences?: UserPreferences; error?: string }> {
  try {
    const supabase = createClient()
    
    // Build update object with only provided fields
    const updateData: any = {}
    
    if (updates.firstName !== undefined) updateData.first_name = updates.firstName.trim()
    if (updates.lastName !== undefined) updateData.last_name = updates.lastName.trim()
    if (updates.username !== undefined) updateData.username = updates.username.trim()
    if (updates.displayName !== undefined) updateData.display_name = updates.displayName?.trim() || null
    if (updates.timezone !== undefined) updateData.timezone = updates.timezone
    if (updates.theme !== undefined) updateData.theme = updates.theme
    if (updates.processContext !== undefined) updateData.process_context = updates.processContext
    if (updates.featuresEnabled !== undefined) updateData.features_enabled = updates.featuresEnabled
    
    const { data, error } = await supabase
      .from('user_preferences')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating user preferences:', error)
      
      // Provide specific error messages for common issues
      if (error.code === '23505' && error.message.includes('username')) {
        return {
          success: false,
          error: 'Username is already taken'
        }
      }
      
      return {
        success: false,
        error: 'Failed to update user preferences'
      }
    }
    
    return {
      success: true,
      preferences: transformDatabaseToPreferences(data)
    }
    
  } catch (error) {
    console.error('Update user preferences failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Associates user with an organization (formal membership)
 */
export async function associateUserWithOrganization(
  userId: string,
  organizationId: string,
  isOrgMember: boolean = true
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .update({
        org_id: organizationId,
        is_org_member: isOrgMember
      })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error associating user with organization:', error)
      return {
        success: false,
        error: 'Failed to associate user with organization'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Associate user with organization failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Updates user account tier and subscription status
 */
export async function updateUserSubscription(
  userId: string,
  accountTier: 'free' | 'pro' | 'team' | 'enterprise',
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid'
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .update({
        account_tier: accountTier,
        subscription_status: subscriptionStatus
      })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error updating user subscription:', error)
      return {
        success: false,
        error: 'Failed to update user subscription'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Update user subscription failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Gets user preferences with organization information
 */
export async function getUserPreferencesWithOrganization(
  userId: string
): Promise<UserPreferencesWithOrg | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select(`
        *,
        organizations (
          id,
          name,
          slug,
          type
        )
      `)
      .eq('user_id', userId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      console.error('Error fetching user preferences with organization:', error)
      return null
    }
    
    const preferences = transformDatabaseToPreferences(data)
    
    return {
      ...preferences,
      organization: data.organizations ? {
        id: data.organizations.id,
        name: data.organizations.name,
        slug: data.organizations.slug,
        type: data.organizations.type
      } : undefined
    }
    
  } catch (error) {
    console.error('Get user preferences with organization failed:', error)
    return null
  }
}

/**
 * Checks if user has completed their preferences setup
 */
export async function hasUserCompletedPreferences(userId: string): Promise<boolean> {
  try {
    const preferences = await getUserPreferences(userId)
    
    if (!preferences) {
      return false
    }
    
    // Check if required fields are completed
    return !!(
      preferences.firstName &&
      preferences.lastName &&
      preferences.username &&
      preferences.timezone &&
      preferences.theme &&
      preferences.processContext
    )
    
  } catch (error) {
    console.error('Error checking user preferences completion:', error)
    return false
  }
}

/**
 * Deletes user preferences (for account deletion)
 */
export async function deleteUserPreferences(userId: string): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .delete()
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error deleting user preferences:', error)
      return {
        success: false,
        error: 'Failed to delete user preferences'
      }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Delete user preferences failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Transforms database row to UserPreferences object
 */
function transformDatabaseToPreferences(data: any): UserPreferences {
  return {
    id: data.id,
    userId: data.user_id,
    orgId: data.org_id,
    isOrgMember: data.is_org_member,
    firstName: data.first_name,
    lastName: data.last_name,
    username: data.username,
    displayName: data.display_name,
    timezone: data.timezone,
    theme: data.theme,
    processContext: data.process_context,
    accountTier: data.account_tier,
    subscriptionStatus: data.subscription_status,
    featuresEnabled: data.features_enabled || {},
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}

/**
 * Gets enhanced feature availability based on account tier with organization features
 */
export function getFeatureAvailability(accountTier: string) {
  const tierAccess = TIER_ACCESS[accountTier as AccountTier] || TIER_ACCESS.free
  
  const features = {
    free: {
      maxRecords: 50,
      publicProfile: false,
      apiAccess: false,
      customFields: 3,
      integrations: 0,
      advancedSearch: false,
      analytics: false,
      exportData: false,
      // Organization features
      canDetectOrganizations: tierAccess.canDetectOrganizations,
      canJoinOrganizations: tierAccess.canJoinOrganizations,
      canCreateOrganizations: tierAccess.canCreateOrganizations,
      canBecomeAdmin: tierAccess.canBecomeAdmin,
      maxOrgMembers: tierAccess.maxOrgMembers,
      adminCapabilities: tierAccess.adminCapabilities
    },
    pro: {
      maxRecords: 500,
      publicProfile: true,
      apiAccess: true,
      customFields: 10,
      integrations: 2,
      advancedSearch: true,
      analytics: true,
      exportData: true,
      // Organization features
      canDetectOrganizations: tierAccess.canDetectOrganizations,
      canJoinOrganizations: tierAccess.canJoinOrganizations,
      canCreateOrganizations: tierAccess.canCreateOrganizations,
      canBecomeAdmin: tierAccess.canBecomeAdmin,
      maxOrgMembers: tierAccess.maxOrgMembers,
      adminCapabilities: tierAccess.adminCapabilities
    },
    team: {
      maxRecords: -1, // Unlimited
      publicProfile: true,
      apiAccess: true,
      customFields: -1, // Unlimited
      integrations: 5,
      advancedSearch: true,
      analytics: true,
      exportData: true,
      // Organization features
      canDetectOrganizations: tierAccess.canDetectOrganizations,
      canJoinOrganizations: tierAccess.canJoinOrganizations,
      canCreateOrganizations: tierAccess.canCreateOrganizations,
      canBecomeAdmin: tierAccess.canBecomeAdmin,
      maxOrgMembers: tierAccess.maxOrgMembers,
      adminCapabilities: tierAccess.adminCapabilities
    },
    enterprise: {
      maxRecords: -1, // Unlimited
      publicProfile: true,
      apiAccess: true,
      customFields: -1, // Unlimited
      integrations: -1, // Unlimited
      advancedSearch: true,
      analytics: true,
      exportData: true,
      // Organization features
      canDetectOrganizations: tierAccess.canDetectOrganizations,
      canJoinOrganizations: tierAccess.canJoinOrganizations,
      canCreateOrganizations: tierAccess.canCreateOrganizations,
      canBecomeAdmin: tierAccess.canBecomeAdmin,
      maxOrgMembers: tierAccess.maxOrgMembers,
      adminCapabilities: tierAccess.adminCapabilities
    }
  }
  
  return features[accountTier as keyof typeof features] || features.free
}


/**
 * Enhanced Profile Management Functions
 * 
 * These functions extend the basic user preferences with comprehensive
 * profile management capabilities including enhanced data, activity tracking,
 * and profile completeness calculations.
 */

/**
 * Gets enhanced user profile by user ID
 */
export async function getEnhancedUserProfile(userId: string): Promise<any | null> {
  try {
    const supabase = createClient()
    
    const { data, error } = await supabase
      .from('user_preferences')
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error) {
      if (error.code === 'PGRST116') { // No rows returned
        return null
      }
      console.error('Error fetching enhanced user profile:', error)
      return null
    }
    
    return transformDatabaseToEnhancedProfile(data)
    
  } catch (error) {
    console.error('Get enhanced user profile failed:', error)
    return null
  }
}

/**
 * Updates enhanced user profile
 */
export async function updateEnhancedUserProfile(
  userId: string,
  updates: any,
  request?: Request
): Promise<{ success: boolean; profile?: any; error?: string }> {
  try {
    const supabase = createClient()
    
    // Build update object with only provided fields
    const updateData: any = {}
    
    // Basic profile fields
    if (updates.firstName !== undefined) updateData.first_name = updates.firstName.trim()
    if (updates.lastName !== undefined) updateData.last_name = updates.lastName.trim()
    if (updates.username !== undefined) updateData.username = updates.username.trim()
    if (updates.displayName !== undefined) updateData.display_name = updates.displayName?.trim() || null
    if (updates.timezone !== undefined) updateData.timezone = updates.timezone
    if (updates.theme !== undefined) updateData.theme = updates.theme
    if (updates.processContext !== undefined) updateData.process_context = updates.processContext
    if (updates.featuresEnabled !== undefined) updateData.features_enabled = updates.featuresEnabled
    
    // Enhanced profile fields
    if (updates.profilePictureUrl !== undefined) updateData.profile_picture_url = updates.profilePictureUrl
    if (updates.bio !== undefined) updateData.bio = updates.bio?.trim() || null
    if (updates.jobTitle !== undefined) updateData.job_title = updates.jobTitle?.trim() || null
    if (updates.phone !== undefined) updateData.phone = updates.phone?.trim() || null
    if (updates.linkedinUrl !== undefined) updateData.linkedin_url = updates.linkedinUrl?.trim() || null
    if (updates.websiteUrl !== undefined) updateData.website_url = updates.websiteUrl?.trim() || null
    if (updates.location !== undefined) updateData.location = updates.location?.trim() || null
    
    // Handle notification preferences merge
    if (updates.notificationPreferences !== undefined) {
      const currentProfile = await getEnhancedUserProfile(userId)
      const currentNotifications = currentProfile?.notificationPreferences || {
        email_notifications: true,
        browser_notifications: true,
        workflow_updates: true,
        organization_updates: true,
        security_alerts: true
      }
      updateData.notification_preferences = {
        ...currentNotifications,
        ...updates.notificationPreferences
      }
    }
    
    // Handle privacy settings merge
    if (updates.privacySettings !== undefined) {
      const currentProfile = await getEnhancedUserProfile(userId)
      const currentPrivacy = currentProfile?.privacySettings || {
        profile_visibility: 'organization',
        show_email: false,
        show_phone: false,
        show_location: true
      }
      updateData.privacy_settings = {
        ...currentPrivacy,
        ...updates.privacySettings
      }
    }
    
    const { data, error } = await supabase
      .from('user_preferences')
      .update(updateData)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      console.error('Error updating enhanced user profile:', error)
      
      // Provide specific error messages for common issues
      if (error.code === '23505' && error.message.includes('username')) {
        return {
          success: false,
          error: 'Username is already taken'
        }
      }
      
      return {
        success: false,
        error: 'Failed to update profile'
      }
    }
    
    // Track the profile update activity (basic tracking without circular import)
    try {
      await supabase
        .from('user_activity_logs')
        .insert({
          user_id: userId,
          activity_type: 'profile_updated',
          activity_data: { updated_fields: Object.keys(updateData) },
          ip_address: request?.headers.get('x-forwarded-for')?.split(',')[0]?.trim(),
          user_agent: request?.headers.get('user-agent')
        })
    } catch (activityError) {
      console.error('Failed to track activity:', activityError)
      // Don't fail the main operation if activity tracking fails
    }
    
    return {
      success: true,
      profile: transformDatabaseToEnhancedProfile(data)
    }
    
  } catch (error) {
    console.error('Update enhanced user profile failed:', error)
    return {
      success: false,
      error: 'An unexpected error occurred'
    }
  }
}

/**
 * Transforms database row to EnhancedUserProfile object
 */
function transformDatabaseToEnhancedProfile(data: any): any {
  const DEFAULT_NOTIFICATION_PREFERENCES = {
    email_notifications: true,
    browser_notifications: true,
    workflow_updates: true,
    organization_updates: true,
    security_alerts: true
  }

  const DEFAULT_PRIVACY_SETTINGS = {
    profile_visibility: 'organization',
    show_email: false,
    show_phone: false,
    show_location: true
  }

  return {
    id: data.id,
    userId: data.user_id,
    orgId: data.org_id,
    isOrgMember: data.is_org_member,
    firstName: data.first_name,
    lastName: data.last_name,
    username: data.username,
    displayName: data.display_name,
    timezone: data.timezone,
    theme: data.theme,
    processContext: data.process_context,
    accountTier: data.account_tier,
    subscriptionStatus: data.subscription_status,
    featuresEnabled: data.features_enabled || {},
    
    // Enhanced profile fields
    profilePictureUrl: data.profile_picture_url,
    bio: data.bio,
    jobTitle: data.job_title,
    phone: data.phone,
    linkedinUrl: data.linkedin_url,
    websiteUrl: data.website_url,
    location: data.location,
    notificationPreferences: data.notification_preferences || DEFAULT_NOTIFICATION_PREFERENCES,
    privacySettings: data.privacy_settings || DEFAULT_PRIVACY_SETTINGS,
    lastActive: data.last_active,
    profileCompleteness: data.profile_completeness || 0,
    
    // Timestamps
    createdAt: data.created_at,
    updatedAt: data.updated_at
  }
}
/**
 * Updates user's last active timestamp
 */
export async function updateLastActive(userId: string): Promise<{ success: boolean }> {
  try {
    const supabase = createClient()
    
    const { error } = await supabase
      .from('user_preferences')
      .update({ last_active: new Date().toISOString() })
      .eq('user_id', userId)
    
    if (error) {
      console.error('Error updating last active:', error)
      return { success: false }
    }
    
    return { success: true }
    
  } catch (error) {
    console.error('Update last active failed:', error)
    return { success: false }
  }
}