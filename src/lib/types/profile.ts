/**
 * Enhanced User Profile Types
 * 
 * TypeScript definitions for the enhanced profile system that extends
 * the basic user preferences with comprehensive profile information.
 */

export interface NotificationPreferences {
  email_notifications: boolean
  browser_notifications: boolean
  workflow_updates: boolean
  organization_updates: boolean
  security_alerts: boolean
}

export interface PrivacySettings {
  profile_visibility: 'private' | 'organization' | 'public'
  show_email: boolean
  show_phone: boolean
  show_location: boolean
}

export interface EnhancedUserProfile {
  // Existing fields from user_preferences
  id: string
  userId: string
  orgId?: string
  isOrgMember: boolean
  firstName: string
  lastName: string
  username: string
  displayName?: string
  timezone: string
  theme: 'light' | 'dark' | 'system'
  processContext: 'recruitment' | 'bench_sales' | 'both'
  accountTier: 'free' | 'pro' | 'team' | 'enterprise'
  subscriptionStatus: 'active' | 'canceled' | 'past_due' | 'unpaid'
  featuresEnabled: Record<string, boolean>
  
  // New enhanced profile fields
  profilePictureUrl?: string
  bio?: string
  jobTitle?: string
  phone?: string
  linkedinUrl?: string
  websiteUrl?: string
  location?: string
  notificationPreferences: NotificationPreferences
  privacySettings: PrivacySettings
  lastActive: string
  profileCompleteness: number
  
  // Timestamps
  createdAt: string
  updatedAt: string
}

export interface EnhancedUserProfileWithOrg extends EnhancedUserProfile {
  organization?: {
    id: string
    name: string
    slug: string
    type: string
  }
}

export interface UpdateEnhancedProfileInput {
  firstName?: string
  lastName?: string
  username?: string
  displayName?: string
  bio?: string
  jobTitle?: string
  phone?: string
  linkedinUrl?: string
  websiteUrl?: string
  location?: string
  profilePictureUrl?: string
  timezone?: string
  theme?: 'light' | 'dark' | 'system'
  processContext?: 'recruitment' | 'bench_sales' | 'both'
  notificationPreferences?: Partial<NotificationPreferences>
  privacySettings?: Partial<PrivacySettings>
  featuresEnabled?: Record<string, boolean>
}

export interface UserActivity {
  id: string
  userId: string
  orgId?: string
  activityType: string
  activityData: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: string
}

export interface UserDashboardStats {
  totalActivities: number
  lastLoginDate: string
  profileCompleteness: number
  organizationRole?: string
  accountTier: string
  subscriptionStatus: string
}

// Activity types for tracking
export const ACTIVITY_TYPES = {
  PROFILE_UPDATED: 'profile_updated',
  PROFILE_PICTURE_CHANGED: 'profile_picture_changed',
  PREFERENCES_UPDATED: 'preferences_updated',
  THEME_CHANGED: 'theme_changed',
  ORGANIZATION_JOINED: 'organization_joined',
  ORGANIZATION_LEFT: 'organization_left',
  LOGIN: 'login',
  LOGOUT: 'logout',
  PASSWORD_CHANGED: 'password_changed',
  TWO_FACTOR_ENABLED: 'two_factor_enabled',
  TWO_FACTOR_DISABLED: 'two_factor_disabled'
} as const

export type ActivityType = typeof ACTIVITY_TYPES[keyof typeof ACTIVITY_TYPES]

// Default preferences
export const DEFAULT_NOTIFICATION_PREFERENCES: NotificationPreferences = {
  email_notifications: true,
  browser_notifications: true,
  workflow_updates: true,
  organization_updates: true,
  security_alerts: true
}

export const DEFAULT_PRIVACY_SETTINGS: PrivacySettings = {
  profile_visibility: 'organization',
  show_email: false,
  show_phone: false,
  show_location: true
}