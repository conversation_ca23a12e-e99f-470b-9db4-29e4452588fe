-- Rollback Migration: User Preferences System
-- Description: Safely removes user_preferences and org_domains tables with data preservation
-- Created: June 25, 2025
-- Rollback for: 003_user_preferences_system.sql

-- =============================================
-- ROLLBACK PROCEDURE
-- =============================================

-- This rollback migration provides a safe way to remove the user preferences system
-- while preserving data integrity and providing recovery options.

-- =============================================
-- 1. DATA BACKUP (OPTIONAL - for safety)
-- =============================================

-- Uncomment these sections if you want to backup data before rollback

/*
-- Create backup tables
CREATE TABLE public.user_preferences_backup AS
SELECT * FROM public.user_preferences;

CREATE TABLE public.org_domains_backup AS  
SELECT * FROM public.org_domains;

-- Add comments to backup tables
COMMENT ON TABLE public.user_preferences_backup IS 'Backup of user_preferences before rollback - created on ' || NOW()::TEXT;
COMMENT ON TABLE public.org_domains_backup IS 'Backup of org_domains before rollback - created on ' || NOW()::TEXT;
*/

-- =============================================
-- 2. DROP TRIGGERS
-- =============================================

-- Drop triggers first to prevent conflicts
DROP TRIGGER IF EXISTS update_user_preferences_updated_at ON public.user_preferences;

-- =============================================
-- 3. DROP UTILITY FUNCTIONS
-- =============================================

-- Drop utility functions created in the migration
DROP FUNCTION IF EXISTS public.detect_org_by_domain(TEXT);
DROP FUNCTION IF EXISTS public.suggest_username(TEXT, TEXT);
DROP FUNCTION IF EXISTS public.is_username_available(TEXT);
DROP FUNCTION IF EXISTS public.update_updated_at_column();

-- =============================================
-- 4. DROP INDEXES
-- =============================================

-- Drop indexes for user_preferences
DROP INDEX IF EXISTS public.idx_user_preferences_account_tier;
DROP INDEX IF EXISTS public.idx_user_preferences_org_member;
DROP INDEX IF EXISTS public.idx_user_preferences_username_lower;
DROP INDEX IF EXISTS public.idx_user_preferences_org_id;
DROP INDEX IF EXISTS public.idx_user_preferences_user_id;

-- Drop indexes for org_domains
DROP INDEX IF EXISTS public.idx_org_domains_auto_join;
DROP INDEX IF EXISTS public.idx_org_domains_org_id;
DROP INDEX IF EXISTS public.idx_org_domains_domain;

-- =============================================
-- 5. DROP RLS POLICIES
-- =============================================

-- Drop RLS policies for user_preferences
DROP POLICY IF EXISTS "Org admins can view org member preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can insert own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can update own preferences" ON public.user_preferences;
DROP POLICY IF EXISTS "Users can view own preferences" ON public.user_preferences;

-- Drop RLS policies for org_domains
DROP POLICY IF EXISTS "Public domain lookup for org detection" ON public.org_domains;
DROP POLICY IF EXISTS "Org admins can manage org domains" ON public.org_domains;

-- =============================================
-- 6. DROP TABLES
-- =============================================

-- Drop tables in reverse dependency order
DROP TABLE IF EXISTS public.user_preferences CASCADE;
DROP TABLE IF EXISTS public.org_domains CASCADE;

-- =============================================
-- 7. CLEANUP VERIFICATION
-- =============================================

-- Verify cleanup completed successfully
DO $$
DECLARE
    table_count INTEGER;
    function_count INTEGER;
BEGIN
    -- Check if tables were dropped
    SELECT COUNT(*) INTO table_count
    FROM information_schema.tables
    WHERE table_schema = 'public'
    AND table_name IN ('user_preferences', 'org_domains');
    
    -- Check if functions were dropped
    SELECT COUNT(*) INTO function_count
    FROM information_schema.routines
    WHERE routine_schema = 'public'
    AND routine_name IN ('is_username_available', 'suggest_username', 'detect_org_by_domain', 'update_updated_at_column');
    
    IF table_count > 0 THEN
        RAISE NOTICE 'WARNING: Some tables were not dropped successfully. Count: %', table_count;
    ELSE
        RAISE NOTICE 'SUCCESS: All tables dropped successfully';
    END IF;
    
    IF function_count > 0 THEN
        RAISE NOTICE 'WARNING: Some functions were not dropped successfully. Count: %', function_count;
    ELSE
        RAISE NOTICE 'SUCCESS: All functions dropped successfully';
    END IF;
END $$;

-- =============================================
-- 8. RECOVERY INSTRUCTIONS
-- =============================================

/*
-- RECOVERY INSTRUCTIONS (if rollback was done by mistake):
-- 
-- 1. If you created backup tables, you can restore data:
--    - Re-run the original migration (003_user_preferences_system.sql)
--    - Restore data from backup tables:
--      INSERT INTO public.user_preferences SELECT * FROM public.user_preferences_backup;
--      INSERT INTO public.org_domains SELECT * FROM public.org_domains_backup;
--    - Drop backup tables:
--      DROP TABLE public.user_preferences_backup;
--      DROP TABLE public.org_domains_backup;
--
-- 2. If no backup was created:
--    - Re-run the original migration (003_user_preferences_system.sql)  
--    - Users will need to re-enter their preferences
--    - Organization domains will need to be re-configured
--
-- 3. Check application compatibility:
--    - Ensure application code handles missing user preferences gracefully
--    - Update any dependent features or UI components
*/

-- Final verification message
SELECT 'User Preferences System rollback completed successfully. Check logs above for any warnings.' AS rollback_status;
