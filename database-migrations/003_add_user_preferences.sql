-- Migration: Add user preferences table for timezone and other user settings
-- This migration adds a user_preferences table to store user-specific settings like timezone

-- User preferences table (stores timezone and other user preferences)
CREATE TABLE user_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT UNIQUE NOT NULL, -- Clerk user ID
  org_id UUID REFERENCES organizations(id), -- Optional: org context for preferences
  
  -- Timezone preferences
  timezone TEXT,
  timezone_auto_detected BOOLEAN DEFAULT false,
  timezone_set_at TIMESTAMP WITH TIME ZONE,
  
  -- Other user preferences (can be extended)
  preferences JSONB DEFAULT '{}',
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS for user preferences
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only access their own preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
  FOR SELECT USING (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "Users can update their own preferences" ON user_preferences
  FOR UPDATE USING (
    user_id = (auth.jwt() -> 'sub')::text
  );

CREATE POLICY "Users can insert their own preferences" ON user_preferences
  FOR INSERT WITH CHECK (
    user_id = (auth.jwt() -> 'sub')::text
  );

-- Create indexes for performance
CREATE INDEX idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX idx_user_preferences_org_id ON user_preferences(org_id);

-- Add updated_at trigger
CREATE TRIGGER update_user_preferences_updated_at
  BEFORE UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION handle_updated_at();

-- Update organizations table to include timezone settings
ALTER TABLE organizations ADD COLUMN IF NOT EXISTS timezone_settings JSONB DEFAULT '{
  "default_timezone": "UTC",
  "timezone_policy": "user_choice",
  "allow_user_override": true
}'::jsonb;

-- Function to get effective timezone for a user
CREATE OR REPLACE FUNCTION get_effective_timezone(p_user_id TEXT, p_org_id UUID DEFAULT NULL)
RETURNS TEXT AS $$
DECLARE
  user_timezone TEXT;
  org_timezone TEXT;
  org_policy TEXT;
  allow_override BOOLEAN;
BEGIN
  -- Get user's timezone preference
  SELECT timezone INTO user_timezone 
  FROM user_preferences 
  WHERE user_id = p_user_id;
  
  -- Get organization timezone settings
  IF p_org_id IS NOT NULL THEN
    SELECT 
      (timezone_settings->>'default_timezone')::TEXT,
      (timezone_settings->>'timezone_policy')::TEXT,
      (timezone_settings->>'allow_user_override')::BOOLEAN
    INTO org_timezone, org_policy, allow_override
    FROM organizations 
    WHERE id = p_org_id;
  ELSE
    org_timezone := 'UTC';
    org_policy := 'user_choice';
    allow_override := true;
  END IF;
  
  -- Apply timezone policy
  IF org_policy = 'organization_standard' OR NOT allow_override THEN
    RETURN COALESCE(org_timezone, 'UTC');
  ELSE
    RETURN COALESCE(user_timezone, org_timezone, 'UTC');
  END IF;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
