-- Rollback Migration: Enhanced User Profiles
-- This migration rolls back the enhanced user profiles changes

-- Drop triggers and functions
DROP TRIGGER IF EXISTS trigger_update_profile_completeness ON user_preferences;
DROP FUNCTION IF EXISTS update_profile_completeness();
DROP FUNCTION IF EXISTS calculate_profile_completeness(TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT, TEXT);

-- Drop indexes
DROP INDEX IF EXISTS idx_user_activity_created_at;
DROP INDEX IF EXISTS idx_user_activity_type;
DROP INDEX IF EXISTS idx_user_activity_org_id;
DROP INDEX IF EXISTS idx_user_activity_user_id;
DROP INDEX IF EXISTS idx_user_preferences_completeness;
DROP INDEX IF EXISTS idx_user_preferences_last_active;

-- Drop user activity logs table
DROP TABLE IF EXISTS user_activity_logs;

-- Remove enhanced profile columns from user_preferences
ALTER TABLE user_preferences DROP COLUMN IF EXISTS profile_completeness;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS last_active;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS privacy_settings;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS notification_preferences;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS location;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS website_url;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS linkedin_url;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS phone;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS job_title;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS bio;
ALTER TABLE user_preferences DROP COLUMN IF EXISTS profile_picture_url;