-- Migration: Enhanced User Profiles
-- This migration extends the user_preferences table with complete profile information
-- and adds user activity tracking for enterprise audit requirements

-- Add enhanced profile columns to existing user_preferences table
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS profile_picture_url TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS bio TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS job_title TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS phone TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS linkedin_url TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS website_url TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS location TEXT;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS notification_preferences JSONB DEFAULT '{
  "email_notifications": true,
  "browser_notifications": true,
  "workflow_updates": true,
  "organization_updates": true,
  "security_alerts": true
}'::jsonb;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS privacy_settings JSONB DEFAULT '{
  "profile_visibility": "organization",
  "show_email": false,
  "show_phone": false,
  "show_location": true
}'::jsonb;
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS last_active TIMESTAMPTZ DEFAULT NOW();
ALTER TABLE user_preferences ADD COLUMN IF NOT EXISTS profile_completeness INTEGER DEFAULT 0;

-- Add constraints
ALTER TABLE user_preferences ADD CONSTRAINT IF NOT EXISTS chk_profile_completeness 
  CHECK (profile_completeness >= 0 AND profile_completeness <= 100);

-- Create user activity logs table for audit trails
CREATE TABLE IF NOT EXISTS user_activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id TEXT NOT NULL,
  org_id UUID REFERENCES organizations(id),
  activity_type TEXT NOT NULL,
  activity_data JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Enable RLS for user activity logs
ALTER TABLE user_activity_logs ENABLE ROW LEVEL SECURITY;

-- RLS policies for user activity logs
CREATE POLICY "Users can view their own activity" ON user_activity_logs
  FOR SELECT USING (user_id = (auth.jwt() -> 'sub')::text);

CREATE POLICY "Users can insert their own activity" ON user_activity_logs
  FOR INSERT WITH CHECK (user_id = (auth.jwt() -> 'sub')::text);

-- Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_user_preferences_last_active 
  ON user_preferences(last_active);
CREATE INDEX IF NOT EXISTS idx_user_preferences_completeness 
  ON user_preferences(profile_completeness);

-- Indexes for activity logs
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_org_id ON user_activity_logs(org_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_type ON user_activity_logs(activity_type);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity_logs(created_at);

-- Function to calculate profile completeness
CREATE OR REPLACE FUNCTION calculate_profile_completeness(
  p_first_name TEXT,
  p_last_name TEXT,
  p_username TEXT,
  p_bio TEXT,
  p_job_title TEXT,
  p_phone TEXT,
  p_linkedin_url TEXT,
  p_website_url TEXT,
  p_location TEXT,
  p_profile_picture_url TEXT
)
RETURNS INTEGER AS $$
DECLARE
  completeness INTEGER := 0;
  required_fields INTEGER := 3; -- first_name, last_name, username are required
  optional_fields INTEGER := 7; -- bio, job_title, phone, linkedin_url, website_url, location, profile_picture_url
  total_fields INTEGER := required_fields + optional_fields;
  filled_fields INTEGER := 0;
BEGIN
  -- Count required fields (these should always be filled)
  IF p_first_name IS NOT NULL AND LENGTH(p_first_name) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_last_name IS NOT NULL AND LENGTH(p_last_name) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_username IS NOT NULL AND LENGTH(p_username) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  -- Count optional fields
  IF p_bio IS NOT NULL AND LENGTH(p_bio) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_job_title IS NOT NULL AND LENGTH(p_job_title) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_phone IS NOT NULL AND LENGTH(p_phone) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_linkedin_url IS NOT NULL AND LENGTH(p_linkedin_url) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_website_url IS NOT NULL AND LENGTH(p_website_url) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_location IS NOT NULL AND LENGTH(p_location) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  IF p_profile_picture_url IS NOT NULL AND LENGTH(p_profile_picture_url) > 0 THEN
    filled_fields := filled_fields + 1;
  END IF;
  
  -- Calculate percentage
  completeness := ROUND((filled_fields::DECIMAL / total_fields::DECIMAL) * 100);
  
  RETURN completeness;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update profile completeness
CREATE OR REPLACE FUNCTION update_profile_completeness()
RETURNS TRIGGER AS $$
BEGIN
  NEW.profile_completeness := calculate_profile_completeness(
    NEW.first_name,
    NEW.last_name,
    NEW.username,
    NEW.bio,
    NEW.job_title,
    NEW.phone,
    NEW.linkedin_url,
    NEW.website_url,
    NEW.location,
    NEW.profile_picture_url
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for profile completeness updates
DROP TRIGGER IF EXISTS trigger_update_profile_completeness ON user_preferences;
CREATE TRIGGER trigger_update_profile_completeness
  BEFORE INSERT OR UPDATE ON user_preferences
  FOR EACH ROW EXECUTE FUNCTION update_profile_completeness();